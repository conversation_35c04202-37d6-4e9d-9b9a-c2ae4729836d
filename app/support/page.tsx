import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Footer } from "@/components/footer"
import { MessageSquare, Phone, Mail, Clock, FileText, HelpCircle } from "lucide-react"
import Link from "next/link"

export default function SupportPage() {
  const supportChannels = [
    {
      icon: Phone,
      title: "Phone Support",
      description: "Speak directly with our support team",
      details: "+265 999 123 456",
      availability: "Mon-Fri: 8AM-5PM, Sat: 9AM-1PM",
    },
    {
      icon: Mail,
      title: "Email Support",
      description: "Send us a detailed message",
      details: "<EMAIL>",
      availability: "24-hour response time",
    },
    {
      icon: MessageSquare,
      title: "Live Chat",
      description: "Chat with us in real-time",
      details: "Available on website",
      availability: "Mon-Fri: 8AM-5PM",
    },
  ]

  const supportCategories = [
    {
      icon: HelpCircle,
      title: "Technical Support",
      description: "Get help with technical issues, bugs, or system errors",
      link: "/support/technical",
    },
    {
      icon: FileText,
      title: "Billing & Accounts",
      description: "Questions about invoices, payments, or account management",
      link: "/support/billing",
    },
    {
      icon: Clock,
      title: "Service Status",
      description: "Check the current status of our services and infrastructure",
      link: "/support/status",
    },
  ]

  return (
    <div className="min-h-screen pt-16">
      <section className="py-16 lg:py-20 bg-gradient-to-br from-primary/5 via-background to-accent/5">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl lg:text-4xl font-bold text-foreground mb-6 text-balance">
              How Can We <span className="text-primary">Help You?</span>
            </h1>
            <p className="text-base lg:text-lg text-muted-foreground text-pretty leading-relaxed">
              Our support team is here to assist you with any questions or issues. Choose your preferred way to reach
              us.
            </p>
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3">Contact Support</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Multiple ways to get in touch with our support team
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
            {supportChannels.map((channel, index) => {
              const Icon = channel.icon
              return (
                <Card key={index} className="border-2 hover:border-primary transition-colors">
                  <CardContent className="p-6 text-center">
                    <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                      <Icon className="w-8 h-8 text-primary" />
                    </div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">{channel.title}</h3>
                    <p className="text-sm text-muted-foreground mb-3">{channel.description}</p>
                    <p className="text-sm font-semibold text-primary mb-2">{channel.details}</p>
                    <p className="text-xs text-muted-foreground">{channel.availability}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <h2 className="text-xl lg:text-2xl font-bold text-foreground mb-6">Submit a Support Ticket</h2>
              <form className="space-y-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-foreground mb-2">
                    Full Name
                  </label>
                  <Input id="name" placeholder="John Doe" required />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-foreground mb-2">
                    Email Address
                  </label>
                  <Input id="email" type="email" placeholder="<EMAIL>" required />
                </div>

                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-foreground mb-2">
                    Support Category
                  </label>
                  <select
                    id="category"
                    className="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground"
                    required
                  >
                    <option value="">Select a category</option>
                    <option value="technical">Technical Support</option>
                    <option value="billing">Billing & Accounts</option>
                    <option value="hosting">Hosting Issues</option>
                    <option value="domain">Domain Management</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="priority" className="block text-sm font-medium text-foreground mb-2">
                    Priority Level
                  </label>
                  <select
                    id="priority"
                    className="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground"
                    required
                  >
                    <option value="low">Low - General inquiry</option>
                    <option value="medium">Medium - Issue affecting work</option>
                    <option value="high">High - Critical issue</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-foreground mb-2">
                    Subject
                  </label>
                  <Input id="subject" placeholder="Brief description of your issue" required />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-foreground mb-2">
                    Detailed Description
                  </label>
                  <Textarea
                    id="message"
                    placeholder="Please provide as much detail as possible about your issue..."
                    rows={6}
                    required
                  />
                </div>

                <Button type="submit" size="lg" className="w-full bg-primary hover:bg-primary/90">
                  Submit Ticket
                </Button>
              </form>
            </div>

            <div className="space-y-6">
              <div>
                <h2 className="text-xl lg:text-2xl font-bold text-foreground mb-6">Quick Links</h2>
                <div className="space-y-4">
                  {supportCategories.map((category, index) => {
                    const Icon = category.icon
                    return (
                      <Card key={index} className="border-2 hover:border-primary transition-colors">
                        <CardContent className="p-6">
                          <Link href={category.link} className="flex items-start gap-4">
                            <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0">
                              <Icon className="w-6 h-6 text-primary" />
                            </div>
                            <div>
                              <h3 className="font-semibold text-foreground mb-1">{category.title}</h3>
                              <p className="text-sm text-muted-foreground">{category.description}</p>
                            </div>
                          </Link>
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
              </div>

              <Card className="bg-primary text-primary-foreground border-0">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold mb-3">Emergency Support</h3>
                  <p className="text-sm text-primary-foreground/90 mb-4">
                    For critical issues affecting your business operations, call our emergency hotline:
                  </p>
                  <p className="text-2xl font-bold mb-4">+265 999 999 999</p>
                  <p className="text-xs text-primary-foreground/80">Available 24/7 for critical issues</p>
                </CardContent>
              </Card>

              <Card className="border-2">
                <CardContent className="p-6">
                  <h3 className="font-semibold text-foreground mb-3">Before Contacting Support</h3>
                  <ul className="text-sm text-muted-foreground space-y-2">
                    <li className="flex items-start gap-2">
                      <span className="text-primary mt-1">•</span>
                      <span>Check our FAQ page for common solutions</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-primary mt-1">•</span>
                      <span>Review our documentation and guides</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-primary mt-1">•</span>
                      <span>Have your account information ready</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-primary mt-1">•</span>
                      <span>Note any error messages you received</span>
                    </li>
                  </ul>
                  <Link href="/faq">
                    <Button variant="outline" size="sm" className="w-full mt-4 bg-transparent">
                      Visit FAQ
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
