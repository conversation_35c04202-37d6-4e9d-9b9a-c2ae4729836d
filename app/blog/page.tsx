import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Footer } from "@/components/footer"
import { Calendar, User, ArrowRight, Tag } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

export default function BlogPage() {
  const featuredPost = {
    title: "The Future of Digital Transformation in Malawi",
    excerpt:
      "Exploring how technology is reshaping businesses across Malawi and what opportunities lie ahead for organizations ready to embrace digital innovation.",
    author: "Chisomo Banda",
    date: "March 15, 2024",
    category: "Digital Transformation",
    image: "/malawi-digital-transformation-technology-business.jpg",
    slug: "future-digital-transformation-malawi",
  }

  const blogPosts = [
    {
      title: "Building Scalable Web Applications with Next.js",
      excerpt:
        "Learn how we use Next.js to build fast, scalable web applications for our clients and why it's become our framework of choice.",
      author: "<PERSON>hab<PERSON> <PERSON><PERSON>",
      date: "March 10, 2024",
      category: "Development",
      image: "/nextjs-web-development-framework.jpg",
      slug: "building-scalable-web-apps-nextjs",
    },
    {
      title: "Cybersecurity Best Practices for Malawian Businesses",
      excerpt:
        "Essential security measures every business should implement to protect their digital assets and customer data.",
      author: "Thandiwe Mwale",
      date: "March 5, 2024",
      category: "Security",
      image: "/cybersecurity-business-protection.jpg",
      slug: "cybersecurity-best-practices",
    },
    {
      title: "Mobile-First Design: Why It Matters in Malawi",
      excerpt:
        "With mobile internet usage dominating in Malawi, we explore why mobile-first design is crucial for business success.",
      author: "Kondwani Chirwa",
      date: "February 28, 2024",
      category: "Design",
      image: "/mobile-first-design-malawi.jpg",
      slug: "mobile-first-design-malawi",
    },
    {
      title: "Case Study: Transforming Water Management with Technology",
      excerpt:
        "How we helped Northern Region Water Board digitize their operations and improve service delivery to 50,000+ customers.",
      author: "Chisomo Banda",
      date: "February 20, 2024",
      category: "Case Study",
      image: "/water-management-case-study.jpg",
      slug: "water-management-case-study",
    },
    {
      title: "Cloud Hosting vs Traditional Hosting: What's Best for You?",
      excerpt:
        "A comprehensive comparison to help you choose the right hosting solution for your business needs and budget.",
      author: "Thabo Phiri",
      date: "February 15, 2024",
      category: "Cloud Services",
      image: "/cloud-hosting-comparison.jpg",
      slug: "cloud-vs-traditional-hosting",
    },
    {
      title: "The Importance of Regular IT Maintenance",
      excerpt:
        "Why proactive IT maintenance is essential for business continuity and how it can save you money in the long run.",
      author: "Thandiwe Mwale",
      date: "February 10, 2024",
      category: "IT Services",
      image: "/it-maintenance-business.jpg",
      slug: "importance-it-maintenance",
    },
  ]

  const categories = [
    "All",
    "Digital Transformation",
    "Development",
    "Security",
    "Design",
    "Case Study",
    "Cloud Services",
    "IT Services",
  ]

  return (
    <div className="min-h-screen pt-16">
      <section className="py-16 lg:py-20 bg-gradient-to-br from-primary/5 via-background to-accent/5">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl lg:text-4xl font-bold text-foreground mb-6 text-balance">
              Insights & <span className="text-primary">Resources</span>
            </h1>
            <p className="text-base lg:text-lg text-muted-foreground text-pretty leading-relaxed">
              Stay updated with the latest trends in technology, best practices, and insights from our team of experts.
            </p>
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <Card className="overflow-hidden border-2 hover:border-primary transition-colors mb-12">
            <div className="grid grid-cols-1 lg:grid-cols-2">
              <div className="relative h-64 lg:h-auto">
                <Image
                  src={featuredPost.image || "/placeholder.svg"}
                  alt={featuredPost.title}
                  fill
                  className="object-cover"
                />
                <div className="absolute top-4 left-4">
                  <span className="px-3 py-1 rounded-full bg-primary text-primary-foreground text-xs font-medium">
                    Featured
                  </span>
                </div>
              </div>
              <CardContent className="p-8 flex flex-col justify-center">
                <div className="flex items-center gap-2 mb-3">
                  <Tag className="w-4 h-4 text-primary" />
                  <span className="text-sm font-medium text-primary">{featuredPost.category}</span>
                </div>
                <h2 className="text-2xl font-bold text-foreground mb-4">{featuredPost.title}</h2>
                <p className="text-muted-foreground mb-6 leading-relaxed">{featuredPost.excerpt}</p>
                <div className="flex items-center gap-4 text-sm text-muted-foreground mb-6">
                  <div className="flex items-center gap-1">
                    <User className="w-4 h-4" />
                    <span>{featuredPost.author}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    <span>{featuredPost.date}</span>
                  </div>
                </div>
                <Link href={`/blog/${featuredPost.slug}`}>
                  <Button className="bg-primary hover:bg-primary/90">
                    Read More
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </div>
          </Card>

          <div className="flex flex-wrap gap-2 mb-8">
            {categories.map((category, index) => (
              <Button
                key={index}
                variant={index === 0 ? "default" : "outline"}
                size="sm"
                className={index === 0 ? "bg-primary" : "bg-transparent"}
              >
                {category}
              </Button>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {blogPosts.map((post, index) => (
              <Card key={index} className="overflow-hidden border-2 hover:border-primary transition-colors group">
                <div className="relative h-48">
                  <Image
                    src={post.image || "/placeholder.svg"}
                    alt={post.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="px-3 py-1 rounded-full bg-primary text-primary-foreground text-xs font-medium">
                      {post.category}
                    </span>
                  </div>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-lg font-bold text-foreground mb-2 group-hover:text-primary transition-colors">
                    {post.title}
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4 leading-relaxed">{post.excerpt}</p>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground mb-4">
                    <div className="flex items-center gap-1">
                      <User className="w-3 h-3" />
                      <span>{post.author}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      <span>{post.date}</span>
                    </div>
                  </div>
                  <Link href={`/blog/${post.slug}`}>
                    <Button variant="ghost" size="sm" className="p-0 h-auto text-primary hover:bg-transparent">
                      Read More
                      <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              Load More Articles
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
