import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Footer } from "@/components/footer"
import { Check, X, Mail, Server, HardDrive, Zap, Shield, Clock } from "lucide-react"

export default function PricingPage() {
  const emailPlatforms = [
    { name: "Microsoft 365", popular: true },
    { name: "Google Workspace", popular: false },
    { name: "cPanel Email", popular: false },
  ]

  const emailTiers = [
    {
      name: "Basic",
      price: "MWK 15,000",
      period: "per user/month",
      features: {
        "Microsoft 365": {
          storage: "50 GB mailbox",
          webAccess: true,
          mobileApps: true,
          officeApps: false,
          teams: false,
          support: "Email support",
          uptime: "99.9%",
          security: "Basic spam filtering",
        },
        "Google Workspace": {
          storage: "30 GB storage",
          webAccess: true,
          mobileApps: true,
          officeApps: "Web only",
          teams: "Google Meet (100 participants)",
          support: "Email support",
          uptime: "99.9%",
          security: "Basic security",
        },
        "cPanel Email": {
          storage: "10 GB mailbox",
          webAccess: true,
          mobileApps: true,
          officeApps: false,
          teams: false,
          support: "Email support",
          uptime: "99.5%",
          security: "SpamAssassin",
        },
      },
    },
    {
      name: "Standard",
      price: "MWK 35,000",
      period: "per user/month",
      popular: true,
      features: {
        "Microsoft 365": {
          storage: "50 GB mailbox + 1 TB OneDrive",
          webAccess: true,
          mobileApps: true,
          officeApps: "Desktop apps (Word, Excel, PowerPoint)",
          teams: "Microsoft Teams",
          support: "24/7 phone & email",
          uptime: "99.9%",
          security: "Advanced threat protection",
        },
        "Google Workspace": {
          storage: "2 TB storage",
          webAccess: true,
          mobileApps: true,
          officeApps: "Web & mobile apps",
          teams: "Google Meet (150 participants)",
          support: "24/7 phone & email",
          uptime: "99.9%",
          security: "Advanced security & admin controls",
        },
        "cPanel Email": {
          storage: "50 GB mailbox",
          webAccess: true,
          mobileApps: true,
          officeApps: false,
          teams: false,
          support: "24/7 phone & email",
          uptime: "99.9%",
          security: "Advanced spam & virus protection",
        },
      },
    },
    {
      name: "Pro",
      price: "MWK 55,000",
      period: "per user/month",
      features: {
        "Microsoft 365": {
          storage: "100 GB mailbox + unlimited OneDrive",
          webAccess: true,
          mobileApps: true,
          officeApps: "Desktop apps + advanced features",
          teams: "Microsoft Teams + advanced features",
          support: "Priority 24/7 support",
          uptime: "99.99%",
          security: "Enterprise-grade security & compliance",
        },
        "Google Workspace": {
          storage: "5 TB storage",
          webAccess: true,
          mobileApps: true,
          officeApps: "Full suite with advanced features",
          teams: "Google Meet (500 participants)",
          support: "Priority 24/7 support",
          uptime: "99.99%",
          security: "Enterprise security & DLP",
        },
        "cPanel Email": {
          storage: "Unlimited mailbox",
          webAccess: true,
          mobileApps: true,
          officeApps: false,
          teams: false,
          support: "Priority 24/7 support",
          uptime: "99.99%",
          security: "Enterprise spam & virus protection",
        },
      },
    },
  ]

  const hostingPlans = [
    {
      name: "Starter",
      price: "MWK 25,000",
      period: "per month",
      description: "Perfect for small websites and blogs",
      features: [
        { label: "Storage", value: "10 GB SSD" },
        { label: "Bandwidth", value: "100 GB/month" },
        { label: "Websites", value: "1 website" },
        { label: "Email Accounts", value: "5 accounts" },
        { label: "Databases", value: "2 MySQL databases" },
        { label: "SSL Certificate", value: true },
        { label: "Daily Backups", value: true },
        { label: "cPanel Access", value: true },
        { label: "99.9% Uptime", value: true },
        { label: "Free Domain", value: false },
        { label: "CDN", value: false },
        { label: "Priority Support", value: false },
      ],
    },
    {
      name: "Business",
      price: "MWK 65,000",
      period: "per month",
      description: "Ideal for growing businesses",
      popular: true,
      features: [
        { label: "Storage", value: "50 GB SSD" },
        { label: "Bandwidth", value: "500 GB/month" },
        { label: "Websites", value: "5 websites" },
        { label: "Email Accounts", value: "25 accounts" },
        { label: "Databases", value: "10 MySQL databases" },
        { label: "SSL Certificate", value: true },
        { label: "Daily Backups", value: true },
        { label: "cPanel Access", value: true },
        { label: "99.9% Uptime", value: true },
        { label: "Free Domain", value: true },
        { label: "CDN", value: true },
        { label: "Priority Support", value: true },
      ],
    },
    {
      name: "Enterprise",
      price: "MWK 150,000",
      period: "per month",
      description: "For high-traffic websites and applications",
      features: [
        { label: "Storage", value: "200 GB SSD" },
        { label: "Bandwidth", value: "Unlimited" },
        { label: "Websites", value: "Unlimited" },
        { label: "Email Accounts", value: "Unlimited" },
        { label: "Databases", value: "Unlimited" },
        { label: "SSL Certificate", value: true },
        { label: "Daily Backups", value: true },
        { label: "cPanel Access", value: true },
        { label: "99.99% Uptime", value: true },
        { label: "Free Domain", value: true },
        { label: "CDN", value: true },
        { label: "Priority Support", value: true },
      ],
    },
  ]

  const vpsPlans = [
    {
      name: "VPS Basic",
      price: "MWK 85,000",
      period: "per month",
      features: [
        "2 CPU Cores",
        "4 GB RAM",
        "80 GB SSD Storage",
        "2 TB Bandwidth",
        "1 IPv4 Address",
        "Root Access",
        "Ubuntu/CentOS",
      ],
    },
    {
      name: "VPS Standard",
      price: "MWK 165,000",
      period: "per month",
      popular: true,
      features: [
        "4 CPU Cores",
        "8 GB RAM",
        "160 GB SSD Storage",
        "4 TB Bandwidth",
        "2 IPv4 Addresses",
        "Root Access",
        "Ubuntu/CentOS/Windows",
      ],
    },
    {
      name: "VPS Pro",
      price: "MWK 320,000",
      period: "per month",
      features: [
        "8 CPU Cores",
        "16 GB RAM",
        "320 GB SSD Storage",
        "8 TB Bandwidth",
        "4 IPv4 Addresses",
        "Root Access",
        "Any OS + Managed Service",
      ],
    },
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="pt-32 pb-16 lg:pt-40 lg:pb-24 bg-gradient-to-br from-primary/5 via-background to-accent/5">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl lg:text-6xl font-bold text-foreground mb-6 text-balance">
              Transparent <span className="text-accent">Pricing</span> for Every Business
            </h1>
            <p className="text-lg lg:text-xl text-muted-foreground text-pretty leading-relaxed">
              Choose the perfect plan for your business needs. All prices in Malawi Kwacha. No hidden fees, no
              surprises.
            </p>
          </div>
        </div>
      </section>

      {/* Email Hosting Section */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-accent/10 text-accent text-sm font-medium mb-4">
              <Mail className="w-4 h-4" />
              Professional Email Services
            </div>
            <h2 className="text-3xl lg:text-5xl font-bold text-foreground mb-4 text-balance">Business Email Hosting</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto text-pretty">
              Choose from three leading email platforms. All plans include setup assistance and migration support.
            </p>
          </div>

          {/* Email Pricing Table */}
          <div className="overflow-x-auto">
            <div className="inline-block min-w-full align-middle">
              <div className="overflow-hidden border border-border rounded-lg">
                <table className="min-w-full divide-y divide-border">
                  <thead className="bg-muted">
                    <tr>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-foreground">Feature</th>
                      {emailTiers.map((tier) => (
                        <th key={tier.name} className="px-6 py-4 text-center relative">
                          {tier.popular && (
                            <Badge className="absolute -top-3 left-1/2 -translate-x-1/2 bg-accent text-accent-foreground">
                              Most Popular
                            </Badge>
                          )}
                          <div className="text-lg font-bold text-foreground">{tier.name}</div>
                          <div className="text-2xl font-bold text-accent mt-2">{tier.price}</div>
                          <div className="text-xs text-muted-foreground">{tier.period}</div>
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-border bg-card">
                    {emailPlatforms.map((platform) => (
                      <>
                        <tr key={platform.name} className="bg-muted/50">
                          <td colSpan={4} className="px-6 py-3 text-sm font-semibold text-foreground">
                            {platform.name}
                            {platform.popular && (
                              <Badge variant="outline" className="ml-2 text-xs">
                                Recommended
                              </Badge>
                            )}
                          </td>
                        </tr>
                        <tr key="storage">
                          <td className="px-6 py-3 text-sm text-muted-foreground">Storage</td>
                          {emailTiers.map((tier) => (
                            <td key={tier.name} className="px-6 py-3 text-sm text-center text-foreground">
                              {tier.features[platform.name].storage}
                            </td>
                          ))}
                        </tr>
                        <tr key="officeApps" className="bg-muted/30">
                          <td className="px-6 py-3 text-sm text-muted-foreground">Office Applications</td>
                          {emailTiers.map((tier) => (
                            <td key={tier.name} className="px-6 py-3 text-sm text-center text-foreground">
                              {tier.features[platform.name].officeApps ? (
                                typeof tier.features[platform.name].officeApps === "string" ? (
                                  tier.features[platform.name].officeApps
                                ) : (
                                  <Check className="w-5 h-5 text-accent mx-auto" />
                                )
                              ) : (
                                <X className="w-5 h-5 text-muted-foreground mx-auto" />
                              )}
                            </td>
                          ))}
                        </tr>
                        <tr key="collaborationTools">
                          <td className="px-6 py-3 text-sm text-muted-foreground">Collaboration Tools</td>
                          {emailTiers.map((tier) => (
                            <td key={tier.name} className="px-6 py-3 text-sm text-center text-foreground">
                              {tier.features[platform.name].teams ? (
                                typeof tier.features[platform.name].teams === "string" ? (
                                  tier.features[platform.name].teams
                                ) : (
                                  <Check className="w-5 h-5 text-accent mx-auto" />
                                )
                              ) : (
                                <X className="w-5 h-5 text-muted-foreground mx-auto" />
                              )}
                            </td>
                          ))}
                        </tr>
                        <tr key="support" className="bg-muted/30">
                          <td className="px-6 py-3 text-sm text-muted-foreground">Support</td>
                          {emailTiers.map((tier) => (
                            <td key={tier.name} className="px-6 py-3 text-sm text-center text-foreground">
                              {tier.features[platform.name].support}
                            </td>
                          ))}
                        </tr>
                        <tr key="security">
                          <td className="px-6 py-3 text-sm text-muted-foreground">Security</td>
                          {emailTiers.map((tier) => (
                            <td key={tier.name} className="px-6 py-3 text-sm text-center text-foreground">
                              {tier.features[platform.name].security}
                            </td>
                          ))}
                        </tr>
                      </>
                    ))}
                    <tr className="bg-muted/50">
                      <td className="px-6 py-4"></td>
                      {emailTiers.map((tier) => (
                        <td key={tier.name} className="px-6 py-4 text-center">
                          <Button className={tier.popular ? "bg-accent hover:bg-accent/90 w-full" : "w-full"}>
                            Get Started
                          </Button>
                        </td>
                      ))}
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Web Hosting Section */}
      <section className="py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-accent/10 text-accent text-sm font-medium mb-4">
              <Server className="w-4 h-4" />
              Web Hosting Services
            </div>
            <h2 className="text-3xl lg:text-5xl font-bold text-foreground mb-4 text-balance">Reliable Web Hosting</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto text-pretty">
              Fast, secure, and reliable hosting powered by SSD storage with 99.9% uptime guarantee.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 mb-16">
            {hostingPlans.map((plan) => (
              <Card key={plan.name} className={`relative ${plan.popular ? "border-2 border-accent shadow-xl" : ""}`}>
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 -translate-x-1/2">
                    <Badge className="bg-accent text-accent-foreground">Most Popular</Badge>
                  </div>
                )}
                <CardHeader className="text-center pb-8">
                  <CardTitle className="text-2xl mb-2">{plan.name}</CardTitle>
                  <p className="text-sm text-muted-foreground mb-4">{plan.description}</p>
                  <div className="text-4xl font-bold text-accent">{plan.price}</div>
                  <div className="text-sm text-muted-foreground">{plan.period}</div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start gap-3">
                        {typeof feature.value === "boolean" ? (
                          feature.value ? (
                            <Check className="w-5 h-5 text-accent mt-0.5 flex-shrink-0" />
                          ) : (
                            <X className="w-5 h-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                          )
                        ) : (
                          <Check className="w-5 h-5 text-accent mt-0.5 flex-shrink-0" />
                        )}
                        <div className="flex-1">
                          <span className="text-sm font-medium text-foreground">{feature.label}: </span>
                          <span className="text-sm text-muted-foreground">
                            {typeof feature.value === "boolean" ? "" : feature.value}
                          </span>
                        </div>
                      </li>
                    ))}
                  </ul>
                  <Button className={`w-full ${plan.popular ? "bg-accent hover:bg-accent/90" : ""}`}>
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* VPS Hosting */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-4">
              <HardDrive className="w-4 h-4" />
              VPS Hosting
            </div>
            <h3 className="text-2xl lg:text-4xl font-bold text-foreground mb-4 text-balance">
              Virtual Private Servers
            </h3>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto text-pretty">
              Full root access, dedicated resources, and complete control for your applications.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
            {vpsPlans.map((plan) => (
              <Card key={plan.name} className={plan.popular ? "border-2 border-accent shadow-xl relative" : ""}>
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 -translate-x-1/2">
                    <Badge className="bg-accent text-accent-foreground">Most Popular</Badge>
                  </div>
                )}
                <CardHeader className="text-center pb-8">
                  <CardTitle className="text-2xl mb-4">{plan.name}</CardTitle>
                  <div className="text-4xl font-bold text-accent">{plan.price}</div>
                  <div className="text-sm text-muted-foreground">{plan.period}</div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start gap-3">
                        <Check className="w-5 h-5 text-accent mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-foreground">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button className={`w-full ${plan.popular ? "bg-accent hover:bg-accent/90" : ""}`}>
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 rounded-full bg-accent/10 flex items-center justify-center mx-auto mb-4">
                <Zap className="w-8 h-8 text-accent" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-2">Lightning Fast</h3>
              <p className="text-muted-foreground">SSD storage and optimized servers for maximum performance</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 rounded-full bg-accent/10 flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-accent" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-2">Secure & Protected</h3>
              <p className="text-muted-foreground">Free SSL certificates, daily backups, and DDoS protection</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 rounded-full bg-accent/10 flex items-center justify-center mx-auto mb-4">
                <Clock className="w-8 h-8 text-accent" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-2">24/7 Support</h3>
              <p className="text-muted-foreground">Local Malawian support team available round the clock</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 lg:py-24 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4 text-balance">Need a Custom Solution?</h2>
          <p className="text-lg text-primary-foreground/90 mb-8 max-w-2xl mx-auto text-pretty">
            We offer custom packages for enterprises and organizations with specific requirements. Contact us for a
            tailored quote.
          </p>
          <Button size="lg" className="bg-accent hover:bg-accent/90 text-accent-foreground">
            Contact Sales Team
          </Button>
        </div>
      </section>

      <Footer />
    </div>
  )
}
