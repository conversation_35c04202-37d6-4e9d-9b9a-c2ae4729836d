import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Footer } from "@/components/footer"
import { Users, Calendar, MessageCircle, Award, Lightbulb, Code } from "lucide-react"

export default function CommunityPage() {
  const initiatives = [
    {
      icon: Code,
      title: "Developer Meetups",
      description:
        "Monthly gatherings for developers to share knowledge, network, and learn about the latest technologies.",
      frequency: "Monthly",
      nextEvent: "April 15, 2024",
    },
    {
      icon: Lightbulb,
      title: "Tech Talks & Workshops",
      description:
        "Free workshops and presentations on various technology topics, from web development to cybersecurity.",
      frequency: "Quarterly",
      nextEvent: "April 20, 2024",
    },
    {
      icon: Users,
      title: "Internship Program",
      description:
        "Providing hands-on experience to students and recent graduates interested in software development and IT.",
      frequency: "Ongoing",
      nextEvent: "Applications Open",
    },
    {
      icon: Award,
      title: "Innovation Challenge",
      description:
        "Annual competition for students and developers to showcase innovative solutions to local challenges.",
      frequency: "Annual",
      nextEvent: "August 2024",
    },
  ]

  const upcomingEvents = [
    {
      title: "Introduction to React Development",
      date: "April 15, 2024",
      time: "2:00 PM - 5:00 PM",
      location: "DreamCode Office, Blantyre",
      type: "Workshop",
      spots: "15 spots remaining",
    },
    {
      title: "Cybersecurity for Small Businesses",
      date: "April 20, 2024",
      time: "10:00 AM - 12:00 PM",
      location: "Online (Zoom)",
      type: "Webinar",
      spots: "Unlimited",
    },
    {
      title: "Mobile App Development with Flutter",
      date: "May 5, 2024",
      time: "2:00 PM - 5:00 PM",
      location: "DreamCode Office, Blantyre",
      type: "Workshop",
      spots: "12 spots remaining",
    },
  ]

  const testimonials = [
    {
      quote:
        "The internship program at DreamCode gave me practical skills that I couldn't learn in the classroom. I'm now working as a full-time developer!",
      author: "Chifundo Mbewe",
      role: "Former Intern, Now Software Developer",
    },
    {
      quote:
        "The developer meetups are invaluable. I've learned so much from the community and made great professional connections.",
      author: "Precious Banda",
      role: "Freelance Developer",
    },
    {
      quote:
        "Winning the Innovation Challenge was a turning point for my startup. The mentorship and exposure helped us secure funding.",
      author: "Yamikani Phiri",
      role: "Tech Entrepreneur",
    },
  ]

  return (
    <div className="min-h-screen pt-16">
      <section className="py-16 lg:py-20 bg-gradient-to-br from-primary/5 via-background to-accent/5">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl lg:text-4xl font-bold text-foreground mb-6 text-balance">
              Join Our <span className="text-primary">Community</span>
            </h1>
            <p className="text-base lg:text-lg text-muted-foreground mb-8 text-pretty leading-relaxed">
              We're committed to growing Malawi's tech ecosystem through education, mentorship, and community building.
              Join us in shaping the future of technology in Malawi.
            </p>
            <Button size="lg" className="bg-primary hover:bg-primary/90">
              Get Involved
            </Button>
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3">Community Initiatives</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Programs and events designed to support and grow the tech community
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {initiatives.map((initiative, index) => {
              const Icon = initiative.icon
              return (
                <Card key={index} className="border-2 hover:border-primary transition-colors">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0">
                        <Icon className="w-6 h-6 text-primary" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-foreground mb-2">{initiative.title}</h3>
                        <p className="text-sm text-muted-foreground mb-4 leading-relaxed">{initiative.description}</p>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            <span>{initiative.frequency}</span>
                          </div>
                          <div className="px-2 py-1 rounded bg-primary/10 text-primary font-medium">
                            {initiative.nextEvent}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20 bg-muted/30">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3">Upcoming Events</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Register for our upcoming workshops, meetups, and webinars
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {upcomingEvents.map((event, index) => (
              <Card key={index} className="border-2 hover:border-primary transition-colors">
                <CardContent className="p-6">
                  <div className="mb-3">
                    <span className="px-3 py-1 rounded-full bg-primary/10 text-primary text-xs font-medium">
                      {event.type}
                    </span>
                  </div>
                  <h3 className="text-lg font-semibold text-foreground mb-3">{event.title}</h3>
                  <div className="space-y-2 text-sm text-muted-foreground mb-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-primary" />
                      <span>{event.date}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-primary" />
                      <span>{event.time}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4 text-primary" />
                      <span>{event.location}</span>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground mb-4">{event.spots}</p>
                  <Button className="w-full bg-primary hover:bg-primary/90">Register Now</Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3">Community Stories</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Hear from members of our community about their experiences
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="border-2">
                <CardContent className="p-6">
                  <MessageCircle className="w-8 h-8 text-primary mb-4" />
                  <p className="text-sm text-muted-foreground mb-6 leading-relaxed italic">"{testimonial.quote}"</p>
                  <div>
                    <p className="font-semibold text-foreground">{testimonial.author}</p>
                    <p className="text-xs text-muted-foreground">{testimonial.role}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 lg:px-8 text-center">
          <h2 className="text-2xl lg:text-3xl font-bold mb-4">Want to Get Involved?</h2>
          <p className="text-base text-primary-foreground/90 mb-8 max-w-2xl mx-auto">
            Whether you're a student, professional developer, or tech enthusiast, there's a place for you in our
            community.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-accent hover:bg-accent/90 text-accent-foreground">
              Join Our Community
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground/10"
            >
              View Calendar
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
