"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Footer } from "@/components/footer"
import { ChevronDown, Search } from "lucide-react"
import { useState } from "react"
import { Input } from "@/components/ui/input"

export default function FAQPage() {
  const [openIndex, setOpenIndex] = useState<number | null>(0)
  const [searchQuery, setSearchQuery] = useState("")

  const faqCategories = [
    {
      category: "General Questions",
      questions: [
        {
          question: "What services does DreamCode Malawi offer?",
          answer:
            "We offer a comprehensive range of technology services including custom software development (web and mobile applications), cloud hosting and VPS services, domain registration (.mw domains), IT infrastructure setup and support, cybersecurity services, and Microsoft 365 solutions. We also provide ongoing maintenance and support for all our solutions.",
        },
        {
          question: "Where are you located?",
          answer:
            "Our main office is located on Victoria Avenue in Blantyre, Malawi. However, we serve clients across the entire country and can provide both remote and on-site support depending on your needs.",
        },
        {
          question: "How long have you been in business?",
          answer:
            "DreamCode Malawi was founded in 2017. We have over 7 years of experience delivering technology solutions to businesses across Malawi, with more than 150 successful projects completed.",
        },
        {
          question: "What industries do you serve?",
          answer:
            "We serve a diverse range of industries including water utilities, healthcare, financial services, education, manufacturing, government, and NGOs. Our solutions are tailored to meet the specific needs of each sector.",
        },
      ],
    },
    {
      category: "Software Development",
      questions: [
        {
          question: "How long does it take to develop a custom application?",
          answer:
            "Development timelines vary based on project complexity. A simple mobile app might take 2-3 months, while a complex enterprise system could take 6-12 months. We provide detailed timelines during the planning phase and use agile methodology to deliver working features incrementally.",
        },
        {
          question: "Do you provide ongoing support after development?",
          answer:
            "Yes, we offer comprehensive maintenance and support packages for all our software solutions. This includes bug fixes, security updates, feature enhancements, and technical support. We have different support tiers to match your needs and budget.",
        },
        {
          question: "Can you integrate with our existing systems?",
          answer:
            "Absolutely. We have extensive experience integrating new solutions with existing systems through APIs, databases, and other integration methods. We'll assess your current infrastructure and design the best integration approach.",
        },
        {
          question: "What technologies do you use?",
          answer:
            "We use modern, industry-standard technologies including React and Next.js for web applications, Flutter and React Native for mobile apps, Node.js and Python for backend development, and PostgreSQL and MongoDB for databases. We select technologies based on your specific requirements.",
        },
      ],
    },
    {
      category: "Hosting & Domains",
      questions: [
        {
          question: "What is included in your hosting packages?",
          answer:
            "Our hosting packages include SSD storage, bandwidth allocation, free SSL certificates, daily backups, email hosting, 24/7 support, and a 99.9% uptime guarantee. Higher-tier plans include additional features like priority support and advanced security.",
        },
        {
          question: "How do I register a .mw domain?",
          answer:
            "As an accredited SMDP distributor, we can register .mw domains on your behalf. Simply use our domain search tool to check availability, then complete the registration process. We'll handle all the paperwork and technical setup.",
        },
        {
          question: "Can I transfer my existing website to your hosting?",
          answer:
            "Yes, we offer free website migration services for new hosting customers. Our team will handle the entire transfer process to ensure minimal downtime and a smooth transition.",
        },
        {
          question: "What happens if my website goes down?",
          answer:
            "We monitor all hosted websites 24/7. If an issue occurs, our team is automatically notified and begins working on a resolution immediately. We also maintain regular backups so we can quickly restore your site if needed.",
        },
      ],
    },
    {
      category: "IT Services",
      questions: [
        {
          question: "Do you provide on-site IT support?",
          answer:
            "Yes, we provide both remote and on-site IT support across Malawi. Our support packages include scheduled on-site visits, and we can also dispatch technicians for urgent issues as needed.",
        },
        {
          question: "Can you help with network setup for a new office?",
          answer:
            "Absolutely. We provide complete network infrastructure services including design, installation, configuration, and ongoing management. This includes routers, switches, wireless access points, cabling, and security setup.",
        },
        {
          question: "What is included in your IT support packages?",
          answer:
            "Our IT support packages include help desk support (email/phone), remote assistance, system monitoring, software updates, security management, backup management, and regular maintenance. Higher tiers include faster response times and dedicated support staff.",
        },
        {
          question: "Do you offer cybersecurity services?",
          answer:
            "Yes, we provide comprehensive cybersecurity services including security audits, firewall configuration, antivirus deployment, employee training, and ongoing security monitoring to protect your business from cyber threats.",
        },
      ],
    },
    {
      category: "Pricing & Payment",
      questions: [
        {
          question: "How much does custom software development cost?",
          answer:
            "Costs vary significantly based on project scope, complexity, and timeline. Simple applications might start from MWK 2-5 million, while complex enterprise systems can cost MWK 20 million or more. We provide detailed quotes after understanding your requirements.",
        },
        {
          question: "What payment methods do you accept?",
          answer:
            "We accept bank transfers, mobile money (Airtel Money, TNM Mpamba), and cash payments. For larger projects, we typically work with milestone-based payments to spread costs over the development timeline.",
        },
        {
          question: "Do you offer payment plans?",
          answer:
            "Yes, for larger projects we offer flexible payment plans tied to project milestones. We can also discuss monthly payment options for ongoing services like hosting and support.",
        },
        {
          question: "Is there a setup fee for hosting services?",
          answer:
            "No, we don't charge setup fees for our hosting services. You only pay the monthly or annual hosting fee. We also offer free website migration for new customers.",
        },
      ],
    },
  ]

  const filteredFAQs = faqCategories
    .map((category) => ({
      ...category,
      questions: category.questions.filter(
        (q) =>
          q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
          q.answer.toLowerCase().includes(searchQuery.toLowerCase()),
      ),
    }))
    .filter((category) => category.questions.length > 0)

  return (
    <div className="min-h-screen pt-16">
      <section className="py-16 lg:py-20 bg-gradient-to-br from-primary/5 via-background to-accent/5">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl lg:text-4xl font-bold text-foreground mb-6 text-balance">
              Frequently Asked <span className="text-primary">Questions</span>
            </h1>
            <p className="text-base lg:text-lg text-muted-foreground mb-8 text-pretty leading-relaxed">
              Find answers to common questions about our services, pricing, and processes.
            </p>
            <div className="relative max-w-xl mx-auto">
              <Input
                type="search"
                placeholder="Search for answers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-10"
              />
              <Search className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground" />
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="max-w-4xl mx-auto space-y-12">
            {filteredFAQs.map((category, categoryIndex) => (
              <div key={categoryIndex}>
                <h2 className="text-xl lg:text-2xl font-bold text-foreground mb-6">{category.category}</h2>
                <div className="space-y-4">
                  {category.questions.map((faq, index) => {
                    const globalIndex = categoryIndex * 100 + index
                    const isOpen = openIndex === globalIndex
                    return (
                      <Card key={index} className="border-2 hover:border-primary transition-colors">
                        <CardContent className="p-0">
                          <button
                            onClick={() => setOpenIndex(isOpen ? null : globalIndex)}
                            className="w-full p-6 flex items-center justify-between text-left"
                          >
                            <span className="font-semibold text-foreground pr-4">{faq.question}</span>
                            <ChevronDown
                              className={`w-5 h-5 text-primary flex-shrink-0 transition-transform ${isOpen ? "rotate-180" : ""}`}
                            />
                          </button>
                          {isOpen && (
                            <div className="px-6 pb-6">
                              <p className="text-sm text-muted-foreground leading-relaxed">{faq.answer}</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
              </div>
            ))}
          </div>

          {filteredFAQs.length === 0 && (
            <div className="text-center py-12">
              <p className="text-muted-foreground mb-4">No results found for "{searchQuery}"</p>
              <Button variant="outline" onClick={() => setSearchQuery("")}>
                Clear Search
              </Button>
            </div>
          )}
        </div>
      </section>

      <section className="py-16 lg:py-20 bg-muted/30">
        <div className="container mx-auto px-4 lg:px-8">
          <Card className="bg-gradient-to-br from-primary/10 to-accent/10 border-2 border-primary/20">
            <CardContent className="p-8 lg:p-12 text-center">
              <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-4 text-balance">
                Still Have Questions?
              </h2>
              <p className="text-base text-muted-foreground mb-8 max-w-2xl mx-auto text-pretty">
                Can't find the answer you're looking for? Our team is here to help. Get in touch and we'll respond as
                soon as possible.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="bg-primary hover:bg-primary/90">
                  Contact Support
                </Button>
                <Button size="lg" variant="outline">
                  Schedule a Call
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      <Footer />
    </div>
  )
}
