import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Footer } from "@/components/footer"
import { Briefcase, MapPin, Clock, DollarSign, Users, TrendingUp, Heart, Award } from "lucide-react"
import Link from "next/link"

export default function CareersPage() {
  const openPositions = [
    {
      title: "Senior Full-Stack Developer",
      department: "Engineering",
      location: "Blantyre, Malawi",
      type: "Full-time",
      salary: "Competitive",
      description:
        "We're looking for an experienced full-stack developer to join our engineering team and work on exciting projects across multiple industries.",
      requirements: [
        "5+ years of experience in web development",
        "Proficiency in React, Node.js, and databases",
        "Experience with cloud platforms (AWS/Azure)",
        "Strong problem-solving skills",
      ],
    },
    {
      title: "Mobile App Developer",
      department: "Engineering",
      location: "Blantyre, Malawi",
      type: "Full-time",
      salary: "Competitive",
      description:
        "Join our mobile team to build innovative iOS and Android applications that serve thousands of users across Malawi.",
      requirements: [
        "3+ years of mobile development experience",
        "Proficiency in Flutter or React Native",
        "Experience with native iOS/Android development",
        "Portfolio of published apps",
      ],
    },
    {
      title: "UI/UX Designer",
      department: "Design",
      location: "Blantyre, Malawi",
      type: "Full-time",
      salary: "Competitive",
      description:
        "Create beautiful, user-friendly interfaces for web and mobile applications that delight our clients and their users.",
      requirements: [
        "3+ years of UI/UX design experience",
        "Proficiency in Figma and Adobe Creative Suite",
        "Strong portfolio demonstrating design skills",
        "Understanding of user-centered design principles",
      ],
    },
    {
      title: "IT Support Specialist",
      department: "IT Services",
      location: "Blantyre, Malawi",
      type: "Full-time",
      salary: "Competitive",
      description:
        "Provide technical support to our clients, helping them maintain and optimize their IT infrastructure.",
      requirements: [
        "2+ years of IT support experience",
        "Knowledge of Windows Server and Linux",
        "Network configuration experience",
        "Excellent communication skills",
      ],
    },
    {
      title: "Project Manager",
      department: "Operations",
      location: "Blantyre, Malawi",
      type: "Full-time",
      salary: "Competitive",
      description:
        "Lead software development projects from inception to delivery, ensuring quality and client satisfaction.",
      requirements: [
        "3+ years of project management experience",
        "Experience with Agile methodologies",
        "Strong leadership and communication skills",
        "PMP or similar certification preferred",
      ],
    },
  ]

  const benefits = [
    {
      icon: DollarSign,
      title: "Competitive Salary",
      description: "Market-competitive compensation packages with performance bonuses",
    },
    {
      icon: TrendingUp,
      title: "Career Growth",
      description: "Clear career progression paths and opportunities for advancement",
    },
    {
      icon: Users,
      title: "Great Team",
      description: "Work with talented, passionate professionals in a collaborative environment",
    },
    {
      icon: Heart,
      title: "Work-Life Balance",
      description: "Flexible working hours and support for personal well-being",
    },
    {
      icon: Award,
      title: "Learning & Development",
      description: "Training programs, conferences, and continuous learning opportunities",
    },
    {
      icon: Briefcase,
      title: "Exciting Projects",
      description: "Work on diverse, challenging projects that make a real impact",
    },
  ]

  return (
    <div className="min-h-screen pt-16">
      <section className="py-16 lg:py-20 bg-gradient-to-br from-primary/5 via-background to-accent/5">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl lg:text-4xl font-bold text-foreground mb-6 text-balance">
              Join Our <span className="text-primary">Team</span>
            </h1>
            <p className="text-base lg:text-lg text-muted-foreground mb-8 text-pretty leading-relaxed">
              Be part of a dynamic team building innovative technology solutions that transform businesses across
              Malawi. We're always looking for talented individuals who share our passion for excellence.
            </p>
            <Button size="lg" className="bg-primary hover:bg-primary/90">
              View Open Positions
            </Button>
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3">Why Work With Us</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              We offer more than just a job - we provide a platform for growth and impact
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {benefits.map((benefit, index) => {
              const Icon = benefit.icon
              return (
                <Card key={index} className="border-2 hover:border-primary transition-colors">
                  <CardContent className="p-6">
                    <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                      <Icon className="w-6 h-6 text-primary" />
                    </div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">{benefit.title}</h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">{benefit.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20 bg-muted/30">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3">Open Positions</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Explore our current job openings and find your next career opportunity
            </p>
          </div>

          <div className="space-y-6 max-w-4xl mx-auto">
            {openPositions.map((position, index) => (
              <Card key={index} className="border-2 hover:border-primary transition-colors">
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4 mb-4">
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-foreground mb-2">{position.title}</h3>
                      <div className="flex flex-wrap gap-4 text-sm text-muted-foreground mb-3">
                        <div className="flex items-center gap-1">
                          <Briefcase className="w-4 h-4" />
                          <span>{position.department}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <MapPin className="w-4 h-4" />
                          <span>{position.location}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          <span>{position.type}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <DollarSign className="w-4 h-4" />
                          <span>{position.salary}</span>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground mb-4 leading-relaxed">{position.description}</p>
                      <div>
                        <h4 className="text-sm font-semibold text-foreground mb-2">Key Requirements:</h4>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          {position.requirements.map((req, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <span className="text-primary mt-1">•</span>
                              <span>{req}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                    <div className="flex-shrink-0">
                      <Link href={`/careers/${index + 1}`}>
                        <Button className="bg-primary hover:bg-primary/90 w-full lg:w-auto">Apply Now</Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <Card className="bg-gradient-to-br from-primary/10 to-accent/10 border-2 border-primary/20">
            <CardContent className="p-8 lg:p-12 text-center">
              <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-4 text-balance">
                Don't See the Right Position?
              </h2>
              <p className="text-base text-muted-foreground mb-8 max-w-2xl mx-auto text-pretty">
                We're always interested in hearing from talented individuals. Send us your CV and we'll keep you in mind
                for future opportunities.
              </p>
              <Button size="lg" className="bg-primary hover:bg-primary/90">
                Send Your CV
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>

      <Footer />
    </div>
  )
}
