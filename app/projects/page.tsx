import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Footer } from "@/components/footer"
import { ArrowRight, Calendar, Users, TrendingUp, CheckCircle2 } from "lucide-react"

export default function ProjectsPage() {
  const projects = [
    {
      title: "Northern Region Water Board - Maji App",
      category: "Mobile & Web Application",
      client: "Northern Region Water Board",
      year: "2023",
      description:
        "A comprehensive water management system enabling customers to view bills, make payments, report issues, and track water usage in real-time.",
      challenge:
        "NRWB needed to modernize their customer service and reduce manual billing processes while improving payment collection rates.",
      solution:
        "We developed a mobile app and web portal integrated with their existing billing system, featuring mobile money integration, real-time notifications, and a customer service ticketing system.",
      results: [
        "50,000+ active users across Northern Malawi",
        "40% reduction in billing inquiries",
        "Payment collection improved by 35%",
        "Average response time reduced from 5 days to 24 hours",
      ],
      technologies: ["React Native", "Node.js", "PostgreSQL", "Mobile Money API"],
      image: "/water-management-mobile-app-dashboard.jpg",
    },
    {
      title: "AgriConnect Marketplace",
      category: "E-Commerce Platform",
      client: "Malawi Agricultural Cooperative",
      year: "2024",
      description:
        "A digital marketplace connecting smallholder farmers directly with buyers, eliminating middlemen and ensuring fair prices.",
      challenge:
        "Farmers struggled to access markets and get fair prices for their produce, while buyers needed reliable supply chains.",
      solution:
        "Built a mobile-first e-commerce platform with real-time pricing, logistics coordination, and mobile money payments. Includes inventory management and quality verification systems.",
      results: [
        "2,000+ farmers onboarded in first 6 months",
        "MWK 500M+ in transactions processed",
        "Average farmer income increased by 28%",
        "Reduced post-harvest losses by 45%",
      ],
      technologies: ["Flutter", "Firebase", "Stripe", "SMS Gateway"],
      image: "/agricultural-marketplace-mobile-app.jpg",
    },
    {
      title: "MediTrack Hospital Management System",
      category: "Healthcare ERP",
      client: "Blantyre Medical Center",
      year: "2023",
      description:
        "An integrated hospital management system covering patient records, appointments, pharmacy, laboratory, and billing.",
      challenge:
        "Manual record-keeping led to inefficiencies, long wait times, and difficulty tracking patient history across departments.",
      solution:
        "Developed a comprehensive ERP system with electronic health records, appointment scheduling, inventory management, and integrated billing. Includes mobile app for patients.",
      results: [
        "20+ healthcare facilities now using the system",
        "Patient wait time reduced by 60%",
        "Medication errors reduced by 85%",
        "Administrative costs reduced by 30%",
      ],
      technologies: ["Next.js", "PostgreSQL", "Redis", "HL7 FHIR"],
      image: "/hospital-management-dashboard.png",
    },
    {
      title: "EduLearn Online Learning Platform",
      category: "Education Technology",
      client: "Malawi Distance Education Consortium",
      year: "2024",
      description:
        "A comprehensive e-learning platform enabling remote education with video lessons, assignments, assessments, and progress tracking.",
      challenge:
        "Limited access to quality education in rural areas and need for scalable distance learning infrastructure.",
      solution:
        "Created a low-bandwidth optimized learning platform with offline capabilities, video streaming, interactive assessments, and teacher-student communication tools.",
      results: [
        "15,000+ students enrolled",
        "85% course completion rate",
        "Works on 2G networks",
        "Available in English and Chichewa",
      ],
      technologies: ["React", "Django", "AWS", "WebRTC"],
      image: "/online-learning-platform.png",
    },
    {
      title: "FinServe Mobile Banking",
      category: "Financial Services",
      client: "Community Microfinance Bank",
      year: "2023",
      description:
        "A secure mobile banking application enabling customers to manage accounts, transfer money, pay bills, and apply for loans.",
      challenge:
        "Bank needed to expand reach to unbanked populations while reducing operational costs of physical branches.",
      solution:
        "Built a feature-rich mobile banking app with biometric authentication, QR code payments, loan application system, and integration with national payment systems.",
      results: [
        "100,000+ downloads in first year",
        "Processing 150K+ transactions daily",
        "Branch operational costs reduced by 45%",
        "Customer satisfaction score: 4.7/5",
      ],
      technologies: ["React Native", "Java Spring Boot", "MySQL", "Biometric SDK"],
      image: "/mobile-banking-app.png",
    },
    {
      title: "SmartCity Waste Management",
      category: "IoT & Smart City",
      client: "Lilongwe City Council",
      year: "2024",
      description:
        "An IoT-enabled waste management system with smart bins, route optimization, and real-time monitoring for efficient waste collection.",
      challenge:
        "Inefficient waste collection routes, overflowing bins, and lack of data for planning led to poor service delivery.",
      solution:
        "Deployed IoT sensors in waste bins, developed a route optimization algorithm, and created a dashboard for real-time monitoring and analytics.",
      results: [
        "30% reduction in fuel costs",
        "Collection efficiency improved by 40%",
        "Citizen complaints reduced by 55%",
        "Data-driven planning for future infrastructure",
      ],
      technologies: ["IoT Sensors", "Python", "MongoDB", "Google Maps API"],
      image: "/smart-city-waste-management-dashboard.jpg",
    },
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="pt-32 pb-16 lg:pt-40 lg:pb-24 bg-gradient-to-br from-primary/5 via-background to-accent/5">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl lg:text-6xl font-bold text-foreground mb-6 text-balance">
              Projects That <span className="text-accent">Transform Communities</span>
            </h1>
            <p className="text-lg lg:text-xl text-muted-foreground text-pretty leading-relaxed">
              Explore our portfolio of successful digital solutions that are making a real difference across Malawi.
              From healthcare to agriculture, we build technology that matters.
            </p>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-12 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-accent mb-2">50+</div>
              <div className="text-primary-foreground/80">Projects Delivered</div>
            </div>
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-accent mb-2">200K+</div>
              <div className="text-primary-foreground/80">Active Users</div>
            </div>
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-accent mb-2">98%</div>
              <div className="text-primary-foreground/80">Client Satisfaction</div>
            </div>
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-accent mb-2">7+</div>
              <div className="text-primary-foreground/80">Years Experience</div>
            </div>
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="space-y-16 lg:space-y-24">
            {projects.map((project, index) => (
              <div key={index} className={`grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center`}>
                {/* Image */}
                <div className={`${index % 2 === 1 ? "lg:order-2" : ""}`}>
                  <div className="relative rounded-xl overflow-hidden shadow-2xl">
                    <img
                      src={project.image || "/placeholder.svg"}
                      alt={project.title}
                      className="w-full h-auto aspect-[3/2] object-cover"
                    />
                    <div className="absolute top-4 right-4">
                      <Badge className="bg-accent text-accent-foreground">{project.category}</Badge>
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className={`${index % 2 === 1 ? "lg:order-1" : ""}`}>
                  <div className="flex items-center gap-4 mb-4">
                    <Badge variant="outline" className="text-muted-foreground">
                      <Calendar className="w-3 h-3 mr-1" />
                      {project.year}
                    </Badge>
                    <Badge variant="outline" className="text-muted-foreground">
                      <Users className="w-3 h-3 mr-1" />
                      {project.client}
                    </Badge>
                  </div>

                  <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4 text-balance">{project.title}</h2>
                  <p className="text-lg text-muted-foreground mb-6 leading-relaxed">{project.description}</p>

                  <div className="space-y-4 mb-6">
                    <div>
                      <h3 className="font-semibold text-foreground mb-2">The Challenge</h3>
                      <p className="text-muted-foreground leading-relaxed">{project.challenge}</p>
                    </div>
                    <div>
                      <h3 className="font-semibold text-foreground mb-2">Our Solution</h3>
                      <p className="text-muted-foreground leading-relaxed">{project.solution}</p>
                    </div>
                  </div>

                  <div className="mb-6">
                    <h3 className="font-semibold text-foreground mb-3 flex items-center gap-2">
                      <TrendingUp className="w-5 h-5 text-accent" />
                      Key Results
                    </h3>
                    <ul className="space-y-2">
                      {project.results.map((result, idx) => (
                        <li key={idx} className="flex items-start gap-2 text-muted-foreground">
                          <CheckCircle2 className="w-5 h-5 text-accent mt-0.5 flex-shrink-0" />
                          <span>{result}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-6">
                    {project.technologies.map((tech, idx) => (
                      <Badge key={idx} variant="secondary">
                        {tech}
                      </Badge>
                    ))}
                  </div>

                  <Button className="bg-accent hover:bg-accent/90">
                    View Case Study
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-4 lg:px-8">
          <Card className="bg-gradient-to-br from-primary to-primary/80 text-primary-foreground border-0">
            <CardContent className="p-8 lg:p-12 text-center">
              <h2 className="text-3xl lg:text-4xl font-bold mb-4 text-balance">Ready to Start Your Success Story?</h2>
              <p className="text-lg text-primary-foreground/90 mb-8 max-w-2xl mx-auto text-pretty">
                Let's discuss how we can create a custom solution that addresses your unique business challenges and
                drives measurable results.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="bg-accent hover:bg-accent/90 text-accent-foreground">
                  Start Your Project
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="bg-transparent border-primary-foreground/20 hover:bg-primary-foreground/10"
                >
                  Download Portfolio
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      <Footer />
    </div>
  )
}
