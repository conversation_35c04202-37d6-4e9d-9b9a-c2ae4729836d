import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Footer } from "@/components/footer"
import { NewsletterSection } from "@/components/newsletter-section"
import { Target, Eye, Heart, Users, Award, TrendingUp } from "lucide-react"

export default function AboutPage() {
  const values = [
    {
      icon: Target,
      title: "Excellence",
      description: "We strive for excellence in every project, delivering solutions that exceed expectations.",
    },
    {
      icon: Heart,
      title: "Integrity",
      description: "We build trust through transparency, honesty, and ethical business practices.",
    },
    {
      icon: Users,
      title: "Collaboration",
      description: "We work closely with our clients as partners, ensuring their success is our success.",
    },
    {
      icon: TrendingUp,
      title: "Innovation",
      description: "We embrace new technologies and creative approaches to solve complex challenges.",
    },
  ]

  const team = [
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Chief Executive Officer",
      image: "/team-ceo.jpg",
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      role: "Chief Technology Officer",
      image: "/team-cto.jpg",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Head of Development",
      image: "/team-dev.jpg",
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      role: "Head of Operations",
      image: "/team-ops.jpg",
    },
  ]

  return (
    <div className="min-h-screen pt-16">
      <section className="py-16 lg:py-20 bg-gradient-to-br from-primary/5 via-background to-accent/5">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl lg:text-4xl font-bold text-foreground mb-6 text-balance">
              Building Malawi's <span className="text-primary">Digital Future</span>
            </h1>
            <p className="text-base lg:text-lg text-muted-foreground text-pretty leading-relaxed">
              Since 2017, we've been transforming businesses across Malawi with innovative technology solutions that
              drive real results.
            </p>
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-6 text-balance">Our Story</h2>
              <div className="space-y-4 text-muted-foreground leading-relaxed">
                <p>
                  DreamCode Malawi was founded in 2017 with a vision to bridge the digital divide and empower Malawian
                  businesses with world-class technology solutions. What started as a small team of passionate
                  developers has grown into a leading technology solutions provider serving organizations across the
                  country.
                </p>
                <p>
                  We recognized that many businesses in Malawi struggled to access quality software development and IT
                  services. Our mission became clear: provide internationally-standard technology solutions with local
                  understanding and support.
                </p>
                <p>
                  Today, we're proud to serve over 100 clients across various sectors including government, healthcare,
                  finance, education, and utilities. Our solutions impact hundreds of thousands of Malawians daily, from
                  mobile banking apps to water management systems.
                </p>
              </div>
            </div>
            <div className="relative">
              <img
                src="/malawi-technology-office-team-collaboration.jpg"
                alt="DreamCode Malawi Team"
                className="rounded-xl shadow-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-16 lg:py-20 bg-muted/30">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card className="border-2 border-primary/20">
              <CardContent className="p-8">
                <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                  <Eye className="w-8 h-8 text-primary" />
                </div>
                <h3 className="text-xl font-bold text-foreground mb-4">Our Vision</h3>
                <p className="text-muted-foreground leading-relaxed">
                  To become Malawi's most trusted technology partner, recognized for delivering innovative solutions
                  that transform businesses and improve lives across the nation.
                </p>
              </CardContent>
            </Card>

            <Card className="border-2 border-primary/20">
              <CardContent className="p-8">
                <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                  <Target className="w-8 h-8 text-primary" />
                </div>
                <h3 className="text-xl font-bold text-foreground mb-4">Our Mission</h3>
                <p className="text-muted-foreground leading-relaxed">
                  To empower Malawian businesses with world-class technology solutions, exceptional service, and local
                  expertise that drives sustainable growth and digital transformation.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-4 text-balance">Our Core Values</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto text-pretty">
              These principles guide everything we do and shape how we serve our clients.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => {
              const Icon = value.icon
              return (
                <Card key={index} className="text-center hover:shadow-lg transition-all border-2 hover:border-primary">
                  <CardContent className="p-6">
                    <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                      <Icon className="w-8 h-8 text-primary" />
                    </div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">{value.title}</h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">{value.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Stats */}
      <section className="py-16 lg:py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold mb-2">7+</div>
              <div className="text-sm text-primary-foreground/80">Years in Business</div>
            </div>
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold mb-2">150+</div>
              <div className="text-sm text-primary-foreground/80">Projects Delivered</div>
            </div>
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold mb-2">50+</div>
              <div className="text-sm text-primary-foreground/80">Happy Clients</div>
            </div>
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold mb-2">200K+</div>
              <div className="text-sm text-primary-foreground/80">End Users</div>
            </div>
          </div>
        </div>
      </section>

      {/* Team */}
      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-4 text-balance">Meet Our Leadership</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto text-pretty">
              Experienced professionals dedicated to delivering excellence in technology solutions.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-all border-2 hover:border-primary">
                <CardContent className="p-6">
                  <div className="w-32 h-32 rounded-full bg-muted mx-auto mb-4 overflow-hidden">
                    <img
                      src={
                        member.image ||
                        `/placeholder.svg?height=128&width=128&query=${encodeURIComponent(member.name + " professional headshot") || "/placeholder.svg"}`
                      }
                      alt={member.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <h3 className="text-base font-semibold text-foreground mb-1">{member.name}</h3>
                  <p className="text-sm text-muted-foreground">{member.role}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Certifications */}
      <section className="py-16 lg:py-20 bg-muted/30">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-4">
              <Award className="w-4 h-4" />
              Certifications & Partnerships
            </div>
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-4 text-balance">Trusted & Accredited</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="border-2 hover:border-primary transition-colors">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                  <Award className="w-8 h-8 text-primary" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">Microsoft Partner</h3>
                <p className="text-sm text-muted-foreground">
                  Authorized Microsoft 365 distributor and certified partner
                </p>
              </CardContent>
            </Card>

            <Card className="border-2 hover:border-primary transition-colors">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                  <Award className="w-8 h-8 text-primary" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">SMDP Accredited</h3>
                <p className="text-sm text-muted-foreground">Official accredited distributor of .mw domain names</p>
              </CardContent>
            </Card>

            <Card className="border-2 hover:border-primary transition-colors">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                  <Award className="w-8 h-8 text-primary" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">ISO Certified</h3>
                <p className="text-sm text-muted-foreground">Committed to international quality standards</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <Card className="bg-gradient-to-br from-primary/10 to-accent/10 border-2 border-primary/20">
            <CardContent className="p-8 lg:p-12 text-center">
              <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-4 text-balance">
                Join Our Success Story
              </h2>
              <p className="text-base text-muted-foreground mb-8 max-w-2xl mx-auto text-pretty">
                Partner with us to transform your business with innovative technology solutions.
              </p>
              <Button size="lg" className="bg-primary hover:bg-primary/90">
                Get in Touch
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>

      <NewsletterSection />
      <Footer />
    </div>
  )
}
