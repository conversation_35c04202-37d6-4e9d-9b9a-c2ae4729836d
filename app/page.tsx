import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Footer } from "@/components/footer";
import { PartnersCarousel } from "@/components/partners-carousel";
import { NewsletterSection } from "@/components/newsletter-section";
import {
  Smartphone,
  Globe,
  Cpu,
  Shield,
  Server,
  ShoppingCart,
  Mail,
  Cloud,
  Award,
  ArrowRight,
  CheckCircle2,
  Building2,
  Droplet,
  Heart,
  GraduationCap,
  Factory,
  Landmark,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export default function HomePage() {
  const services = [
    {
      icon: Globe,
      title: "Software Systems Development",
      description:
        "Scalable, responsive, and secure software applications built with modern technologies to deliver exceptional user experiences.",
    },
    {
      icon: Cpu,
      title: "Embedded Systems Development",
      description:
        "Custom embedded solutions for IoT devices, industrial automation, and specialized hardware applications.",
    },
    {
      icon: Server,
      title: "Web Hosting & VPS",
      description:
        "Reliable, high-performance hosting solutions with 99.9% uptime guarantee for your critical applications.",
    },
    {
      icon: Mail,
      title: "Professional Email Services",
      description:
        "Business email hosting with Microsoft 365, Google Workspace, and custom email solutions.",
    },
  ];
  const techStacks = [
    { name: "React", logo: "/tech/React.svg" },
    { name: "Node.js", logo: "/tech/nodejs.svg" },
    { name: "Python", logo: "/tech/python.svg" },
    { name: "Flutter", logo: "/tech/flutter.svg" },
    { name: "Next.js", logo: "/tech/nextjs.svg" },
    { name: "PostgreSQL", logo: "/tech/postgres.svg" },
    { name: "MongoDB", logo: "/tech/mongodb.svg" },
    { name: "AWS", logo: "/tech/aws.svg" },
    { name: "Azure", logo: "/tech/azure.svg" },
  ];

  const recentProjects = [
    {
      title: "Northern Region Water Board - Maji App",
      category: "Water Management",
      description:
        "Mobile application for water billing, customer service, and real-time water usage monitoring serving 50,000+ customers.",
      image: "/water-management-mobile-app-dashboard.jpg",
      stats: "50K+ Active Users",
    },
    {
      title: "AgriConnect Marketplace",
      category: "Agriculture",
      description:
        "Digital marketplace connecting farmers directly with buyers, featuring real-time pricing and logistics management.",
      image: "/agricultural-marketplace-mobile-app.jpg",
      stats: "1,000+ Farmers Connected",
    },
    {
      title: "MediCare Hospital System",
      category: "Healthcare",
      description:
        "Comprehensive hospital management system with patient records, appointment scheduling, and billing integration.",
      image: "/hospital-management-dashboard.png",
      stats: "20+ Healthcare Facilities",
    },
  ];

  const industries = [
    {
      icon: Droplet,
      title: "Water & Utilities",
      description:
        "Digital transformation for water boards and utility companies with billing, customer management, and infrastructure monitoring systems.",
      color: "text-blue-500",
    },
    {
      icon: Heart,
      title: "Healthcare",
      description:
        "Patient management systems, telemedicine platforms, and health information systems that improve care delivery across Malawi.",
      color: "text-red-500",
    },
    {
      icon: GraduationCap,
      title: "Education",
      description:
        "Learning management systems, student information systems, and e-learning platforms for schools and universities.",
      color: "text-purple-500",
    },
    {
      icon: Landmark,
      title: "Financial Services",
      description:
        "Secure banking applications, mobile money platforms, and financial management systems with real-time processing.",
      color: "text-green-500",
    },
    {
      icon: Factory,
      title: "Manufacturing",
      description:
        "ERP systems, inventory management, and production tracking solutions that optimize manufacturing operations.",
      color: "text-orange-500",
    },
    {
      icon: Building2,
      title: "Government & NGOs",
      description:
        "Custom solutions for public sector organizations including citizen services, data management, and reporting systems.",
      color: "text-indigo-500",
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section - Reduced heading sizes by 30% and improved Webflow-style layout */}
      <section className="relative pt-24 pb-16 lg:pt-32 lg:pb-24 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-background to-accent/5" />
        <div className="container mx-auto px-4 lg:px-8 relative">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6">
              <Award className="w-4 h-4" />
              Accredited SMDP Distributor of .mw Domains
            </div>
            <h1 className="text-3xl lg:text-4xl xl:text-5xl font-bold text-foreground mb-6 text-balance">
              Transforming Malawian Businesses Through{" "}
              <span className="text-primary">Digital Innovation</span>
            </h1>
            <p className="text-base lg:text-lg text-muted-foreground mb-8 text-pretty leading-relaxed max-w-3xl mx-auto">
              We don't just build software—we create solutions that solve real
              business challenges. From mobile apps to enterprise systems, we
              deliver technology and services that drive growth across Malawi.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-primary hover:bg-primary/90">
                Request a Quote
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button size="lg" variant="outline">
                View Our Work
              </Button>
            </div>

            <div className="grid grid-cols-3 gap-8 mt-16 pt-8 border-t border-border">
              <div>
                <div className="text-2xl lg:text-3xl font-bold text-primary mb-1">
                  150+
                </div>
                <div className="text-sm text-muted-foreground">
                  Projects Delivered
                </div>
              </div>
              <div>
                <div className="text-2xl lg:text-3xl font-bold text-primary mb-1">
                  50+
                </div>
                <div className="text-sm text-muted-foreground">
                  Happy Clients
                </div>
              </div>
              <div>
                <div className="text-2xl lg:text-3xl font-bold text-primary mb-1">
                  7+
                </div>
                <div className="text-sm text-muted-foreground">
                  Years Experience
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Partners Carousel */}
      <PartnersCarousel />

      <section className="py-16 lg:py-20 bg-muted/30">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="flex items-end justify-between mb-12">
            <div>
              <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3 text-balance">
                Recent Projects
              </h2>
              <p className="text-base text-muted-foreground max-w-2xl text-pretty">
                Discover how we've helped organizations across Malawi modernize
                their operations
              </p>
            </div>
            <Link href="/projects" className="hidden md:block">
              <Button variant="outline">
                View All Projects
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
            {recentProjects.map((project, index) => (
              <Card
                key={index}
                className="group overflow-hidden hover:shadow-xl transition-all border-2 hover:border-primary"
              >
                <div className="relative h-48 overflow-hidden bg-muted">
                  <Image
                    src={project.image || "/placeholder.svg"}
                    alt={project.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="px-3 py-1 rounded-full bg-primary text-primary-foreground text-xs font-medium">
                      {project.category}
                    </span>
                  </div>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-primary transition-colors">
                    {project.title}
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4 leading-relaxed">
                    {project.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-primary">
                      {project.stats}
                    </span>
                    <ArrowRight className="w-4 h-4 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-8 md:hidden">
            <Link href="/projects">
              <Button variant="outline" className="w-full bg-transparent">
                View All Projects
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3 text-balance">
              Industries We Serve
            </h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto text-pretty">
              Delivering specialized solutions across key sectors of Malawi's
              economy
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {industries.map((industry, index) => {
              const Icon = industry.icon;
              return (
                <Card
                  key={index}
                  className="group hover:shadow-lg transition-all hover:border-primary"
                >
                  <CardContent className="p-6">
                    <div
                      className={`w-12 h-12 rounded-lg bg-muted flex items-center justify-center mb-4 ${industry.color}`}
                    >
                      <Icon className="w-6 h-6" />
                    </div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">
                      {industry.title}
                    </h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {industry.description}
                    </p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Services Section - Reduced heading sizes */}
      <section className="py-16 lg:py-20 bg-muted/30">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3 text-balance">
              Comprehensive Technology Services
            </h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto text-pretty">
              From concept to deployment, we provide end-to-end technology
              solutions tailored to your business needs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {services.map((service, index) => {
              const Icon = service.icon;
              return (
                <Card
                  key={index}
                  className="group hover:shadow-lg transition-all hover:border-primary"
                >
                  <CardContent className="p-6">
                    <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4 group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                      <Icon className="w-6 h-6 text-primary group-hover:text-primary-foreground" />
                    </div>
                    <h3 className="text-base font-semibold text-foreground mb-2">
                      {service.title}
                    </h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {service.description}
                    </p>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          <div className="text-center mt-12">
            <Link href="/services/cloud">
              <Button size="lg" variant="outline">
                View All Services
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section - Updated button to "Contact Us" and added tech stack visual */}
      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-6 text-balance">
                Why Choose Us for Your Business Needs
              </h2>
              <p className="text-base text-muted-foreground mb-6 leading-relaxed">
                We combine local expertise with international standards to
                deliver solutions that work in the Malawian context. Our team
                understands the unique challenges and opportunities in our
                market.
              </p>
              <ul className="space-y-4 mb-8">
                <li className="flex items-start gap-3">
                  <CheckCircle2 className="w-5 h-5 mt-0.5 flex-shrink-0 text-primary" />
                  <div>
                    <span className="font-semibold text-foreground">
                      Local Expertise, Global Standards
                    </span>
                    <p className="text-sm text-muted-foreground mt-1">
                      Deep understanding of Malawian business environment
                      combined with international best practices
                    </p>
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <CheckCircle2 className="w-5 h-5 mt-0.5 flex-shrink-0 text-primary" />
                  <div>
                    <span className="font-semibold text-foreground">
                      Proven Track Record
                    </span>
                    <p className="text-sm text-muted-foreground mt-1">
                      150+ successful projects across multiple industries in
                      Malawi
                    </p>
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <CheckCircle2 className="w-5 h-5 mt-0.5 flex-shrink-0 text-primary" />
                  <div>
                    <span className="font-semibold text-foreground">
                      Ongoing Support
                    </span>
                    <p className="text-sm text-muted-foreground mt-1">
                      Dedicated support team available to ensure your systems
                      run smoothly
                    </p>
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <CheckCircle2 className="w-5 h-5 mt-0.5 flex-shrink-0 text-primary" />
                  <div>
                    <span className="font-semibold text-foreground">
                      Modern Technology Stack
                    </span>
                    <p className="text-sm text-muted-foreground mt-1">
                      Using cutting-edge technologies to build scalable, secure,
                      and maintainable solutions
                    </p>
                  </div>
                </li>
              </ul>
              <Button size="lg" className="bg-primary hover:bg-primary/90">
                Contact Us
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>

            <div className="relative">
              <Card className="p-8 bg-gradient-to-br from-primary/5 to-accent/5">
                <h3 className="text-lg font-semibold text-foreground mb-6 text-center">
                  Technologies We Use
                </h3>
                <div className="grid grid-cols-3 gap-6">
                  {techStacks.map((tech: any, index: number) => (
                    <div
                      key={index}
                      className="aspect-square rounded-lg bg-background border border-border flex flex-col items-center justify-center gap-2 hover:border-primary hover:shadow-md transition-all"
                    >
                      <img
                        src={tech.logo}
                        alt={tech.name}
                        className="w-50 h-18 object-contain"
                      />
                      {/* <span className="text-xs font-medium text-foreground text-center px-2">
                        {tech.name}
                      </span> */}
                    </div>
                  ))}
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Microsoft 365 & Domain Services - Reduced heading sizes */}
      <section className="py-16 lg:py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-2xl lg:text-3xl font-bold mb-6 text-balance">
                Official Microsoft 365 Partner & .mw Domain Registrar
              </h2>
              <p className="text-primary-foreground/90 text-base mb-6 leading-relaxed">
                As an authorized Microsoft 365 distributor and accredited SMDP
                registrar, we provide complete business productivity solutions
                with training and support.
              </p>
              <ul className="space-y-3 mb-8">
                <li className="flex items-start gap-3">
                  <CheckCircle2 className="w-5 h-5 mt-0.5 flex-shrink-0 text-accent" />
                  <span>Microsoft 365 licensing, deployment, and training</span>
                </li>
                <li className="flex items-start gap-3">
                  <CheckCircle2 className="w-5 h-5 mt-0.5 flex-shrink-0 text-accent" />
                  <span>Official .mw domain registration and management</span>
                </li>
                <li className="flex items-start gap-3">
                  <CheckCircle2 className="w-5 h-5 mt-0.5 flex-shrink-0 text-accent" />
                  <span>
                    Professional email hosting with multiple platform options
                  </span>
                </li>
                <li className="flex items-start gap-3">
                  <CheckCircle2 className="w-5 h-5 mt-0.5 flex-shrink-0 text-accent" />
                  <span>Ongoing technical support and consultation</span>
                </li>
              </ul>
              <Button
                size="lg"
                className="bg-accent hover:bg-accent/90 text-accent-foreground"
              >
                Get Started Today
              </Button>
            </div>
            <div className="relative">
              <div className="aspect-square rounded-2xl bg-primary-foreground/10 backdrop-blur-sm p-8 flex items-center justify-center">
                <div className="text-center">
                  <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-accent flex items-center justify-center">
                    <Award className="w-12 h-12 text-accent-foreground" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">Trusted Partner</h3>
                  <p className="text-primary-foreground/80">
                    Serving businesses across Malawi since 2017
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section - Added testimonials */}
      <section className="py-16 lg:py-20 bg-muted/30">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3 text-balance">
              What Our Clients Say
            </h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto text-pretty">
              Hear from organizations we've helped transform through technology
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                quote:
                  "DreamCode transformed our water billing system. The Maji App has improved our customer service and reduced billing errors significantly.",
                author: "John Banda",
                role: "IT Manager, Northern Region Water Board",
              },
              {
                quote:
                  "Their expertise in healthcare systems helped us digitize our patient records. The system is reliable and easy to use for our staff.",
                author: "Dr. Grace Phiri",
                role: "Director, MediCare Hospital",
              },
              {
                quote:
                  "Professional service from start to finish. They understood our needs and delivered a solution that exceeded our expectations.",
                author: "Peter Mwale",
                role: "CEO, AgriConnect",
              },
            ].map((testimonial, index) => (
              <Card key={index} className="border-2">
                <CardContent className="p-6">
                  <p className="text-sm text-muted-foreground mb-6 leading-relaxed italic">
                    "{testimonial.quote}"
                  </p>
                  <div>
                    <p className="font-semibold text-foreground">
                      {testimonial.author}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {testimonial.role}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-8">
            <Link href="/testimonials">
              <Button variant="outline">
                View All Testimonials
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <NewsletterSection />

      {/* CTA Section - Reduced heading sizes */}
      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <Card className="bg-gradient-to-br from-primary/10 to-accent/10 border-2 border-primary/20">
            <CardContent className="p-8 lg:p-12 text-center">
              <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-4 text-balance">
                Ready to Transform Your Business?
              </h2>
              <p className="text-base text-muted-foreground mb-8 max-w-2xl mx-auto text-pretty">
                Let's discuss how our solutions can help you achieve your
                business goals. Get a free consultation with our experts.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="bg-primary hover:bg-primary/90">
                  Schedule Consultation
                </Button>
                <Button size="lg" variant="outline">
                  View Pricing
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      <Footer />
    </div>
  );
}
