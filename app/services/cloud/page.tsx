import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Footer } from "@/components/footer"
import { CheckCircle2, Server, Mail, Shield, Zap, Clock, HeadphonesIcon, ArrowRight } from "lucide-react"

export default function CloudServicesPage() {
  const hostingPlans = [
    {
      name: "Starter",
      price: "MWK 15,000",
      period: "/month",
      description: "Perfect for small websites and blogs",
      features: [
        "5 GB SSD Storage",
        "50 GB Bandwidth",
        "1 Website",
        "Free SSL Certificate",
        "Daily Backups",
        "24/7 Support",
        "99.9% Uptime Guarantee",
      ],
      popular: false,
    },
    {
      name: "Business",
      price: "MWK 35,000",
      period: "/month",
      description: "Ideal for growing businesses",
      features: [
        "25 GB SSD Storage",
        "200 GB Bandwidth",
        "5 Websites",
        "Free SSL Certificate",
        "Daily Backups",
        "Priority Support",
        "99.9% Uptime Guarantee",
        "Free Domain (.mw)",
      ],
      popular: true,
    },
    {
      name: "Enterprise",
      price: "MWK 75,000",
      period: "/month",
      description: "For high-traffic websites",
      features: [
        "100 GB SSD Storage",
        "Unlimited Bandwidth",
        "Unlimited Websites",
        "Free SSL Certificate",
        "Hourly Backups",
        "Dedicated Support",
        "99.99% Uptime Guarantee",
        "Free Domain (.mw)",
        "Advanced Security",
      ],
      popular: false,
    },
  ]

  const vpsPlans = [
    {
      name: "VPS Basic",
      price: "MWK 50,000",
      period: "/month",
      specs: ["2 CPU Cores", "4 GB RAM", "50 GB SSD", "2 TB Bandwidth"],
    },
    {
      name: "VPS Standard",
      price: "MWK 100,000",
      period: "/month",
      specs: ["4 CPU Cores", "8 GB RAM", "100 GB SSD", "4 TB Bandwidth"],
    },
    {
      name: "VPS Pro",
      price: "MWK 200,000",
      period: "/month",
      specs: ["8 CPU Cores", "16 GB RAM", "200 GB SSD", "8 TB Bandwidth"],
    },
  ]

  const features = [
    {
      icon: Server,
      title: "High-Performance Infrastructure",
      description: "Enterprise-grade servers hosted in secure data centers with redundant power and cooling systems.",
    },
    {
      icon: Shield,
      title: "Advanced Security",
      description: "DDoS protection, firewall configuration, and regular security updates to keep your data safe.",
    },
    {
      icon: Zap,
      title: "Lightning Fast",
      description: "SSD storage and optimized server configurations ensure your websites load quickly.",
    },
    {
      icon: Clock,
      title: "99.9% Uptime",
      description: "Reliable hosting with minimal downtime backed by our service level agreement.",
    },
    {
      icon: HeadphonesIcon,
      title: "24/7 Support",
      description: "Our technical support team is available around the clock to assist you.",
    },
    {
      icon: Mail,
      title: "Email Hosting Included",
      description: "Professional email addresses with your domain name included in all plans.",
    },
  ]

  return (
    <div className="min-h-screen pt-16">
      <section className="py-16 lg:py-20 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl lg:text-4xl font-bold text-foreground mb-4 text-balance">
              Scalable & Secure Cloud Solutions
            </h1>
            <p className="text-base lg:text-lg text-muted-foreground mb-8 text-pretty">
              Reliable web hosting and VPS solutions designed for Malawian businesses. Host your websites, applications,
              and data with confidence.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-primary hover:bg-primary/90">
                Get Started
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button size="lg" variant="outline">
                Contact Sales
              </Button>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3">Why Choose Our Cloud Services</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Enterprise-grade hosting infrastructure with local support
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <Card key={index} className="border-2 hover:border-primary transition-colors">
                  <CardContent className="p-6">
                    <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                      <Icon className="w-6 h-6 text-primary" />
                    </div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">{feature.title}</h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">{feature.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20 bg-muted/30">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3">Web Hosting Plans</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Choose the perfect hosting plan for your website
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
            {hostingPlans.map((plan, index) => (
              <Card
                key={index}
                className={`relative ${plan.popular ? "border-primary border-2 shadow-lg" : "border-2"}`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 -translate-x-1/2">
                    <span className="px-4 py-1 rounded-full bg-primary text-primary-foreground text-xs font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardHeader className="text-center pb-4">
                  <CardTitle className="text-xl font-bold">{plan.name}</CardTitle>
                  <p className="text-sm text-muted-foreground mt-2">{plan.description}</p>
                  <div className="mt-4">
                    <span className="text-3xl font-bold text-foreground">{plan.price}</span>
                    <span className="text-muted-foreground">{plan.period}</span>
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start gap-2">
                        <CheckCircle2 className="w-5 h-5 text-primary flex-shrink-0 mt-0.5" />
                        <span className="text-sm text-foreground">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button
                    className={`w-full ${plan.popular ? "bg-primary" : ""}`}
                    variant={plan.popular ? "default" : "outline"}
                  >
                    Choose Plan
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3">VPS Hosting</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Dedicated resources for applications that need more power and control
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
            {vpsPlans.map((plan, index) => (
              <Card key={index} className="border-2 hover:border-primary transition-colors">
                <CardHeader className="text-center">
                  <CardTitle className="text-xl font-bold">{plan.name}</CardTitle>
                  <div className="mt-4">
                    <span className="text-3xl font-bold text-foreground">{plan.price}</span>
                    <span className="text-muted-foreground">{plan.period}</span>
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 mb-6">
                    {plan.specs.map((spec, idx) => (
                      <li key={idx} className="flex items-start gap-2">
                        <CheckCircle2 className="w-5 h-5 text-primary flex-shrink-0 mt-0.5" />
                        <span className="text-sm text-foreground">{spec}</span>
                      </li>
                    ))}
                  </ul>
                  <Button className="w-full bg-transparent" variant="outline">
                    Choose Plan
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-sm text-muted-foreground mb-4">Need a custom VPS configuration?</p>
            <Button variant="outline">Contact Us for Custom Plans</Button>
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 lg:px-8 text-center">
          <h2 className="text-2xl lg:text-3xl font-bold mb-4">Ready to Get Started?</h2>
          <p className="text-base text-primary-foreground/90 mb-8 max-w-2xl mx-auto">
            Choose your hosting plan and get your website online today. Need help deciding? Our team is here to assist.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-accent hover:bg-accent/90 text-accent-foreground">
              View All Plans
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground/10"
            >
              Talk to Sales
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
