import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Footer } from "@/components/footer"
import { NewsletterSection } from "@/components/newsletter-section"
import {
  Smartphone,
  Globe,
  Cpu,
  Shield,
  Server,
  ShoppingCart,
  Mail,
  Cloud,
  Database,
  Code,
  Briefcase,
  GraduationCap,
  ArrowRight,
  CheckCircle2,
} from "lucide-react"

export default function ServicesPage() {
  const services = [
    {
      icon: Smartphone,
      title: "Mobile Application Development",
      description: "Transform your business with powerful mobile applications that engage users and drive growth.",
      features: [
        "Native iOS and Android development",
        "Cross-platform solutions (React Native, Flutter)",
        "UI/UX design and prototyping",
        "App store deployment and maintenance",
        "Mobile app testing and quality assurance",
        "Integration with backend systems and APIs",
      ],
      useCases: [
        "Customer-facing apps",
        "Internal business tools",
        "E-commerce mobile platforms",
        "Service delivery apps",
      ],
    },
    {
      icon: Globe,
      title: "Web Application Development",
      description:
        "Build scalable, responsive web applications that deliver exceptional user experiences across all devices.",
      features: [
        "Custom web application development",
        "Progressive Web Apps (PWAs)",
        "Single Page Applications (SPAs)",
        "Content Management Systems",
        "API development and integration",
        "Cloud-native architecture",
      ],
      useCases: ["Business portals", "Customer dashboards", "Booking systems", "Data visualization platforms"],
    },
    {
      icon: Cpu,
      title: "Embedded Systems Development",
      description:
        "Custom embedded solutions for IoT devices, industrial automation, and specialized hardware applications.",
      features: [
        "IoT device development",
        "Firmware development",
        "Sensor integration",
        "Real-time systems",
        "Industrial automation",
        "Hardware-software integration",
      ],
      useCases: ["Smart meters", "Agricultural sensors", "Industrial monitoring", "Smart city infrastructure"],
    },
    {
      icon: Shield,
      title: "Systems Security Services",
      description:
        "Comprehensive cybersecurity solutions to protect your digital assets and ensure business continuity.",
      features: [
        "Security audits and assessments",
        "Penetration testing",
        "Security policy development",
        "Incident response planning",
        "Network security implementation",
        "Security awareness training",
      ],
      useCases: ["Financial institutions", "Healthcare organizations", "Government agencies", "E-commerce platforms"],
    },
    {
      icon: Server,
      title: "Web Hosting & VPS Services",
      description:
        "Reliable, high-performance hosting solutions with 99.9% uptime guarantee for your critical applications.",
      features: [
        "Shared hosting plans",
        "Virtual Private Servers (VPS)",
        "Dedicated servers",
        "SSL certificates",
        "Daily backups",
        "24/7 monitoring and support",
      ],
      useCases: ["Business websites", "E-commerce stores", "Web applications", "Development environments"],
    },
    {
      icon: ShoppingCart,
      title: "E-Commerce Solutions",
      description: "Complete e-commerce platforms with payment integration, inventory management, and analytics.",
      features: [
        "Custom e-commerce development",
        "Payment gateway integration",
        "Inventory management systems",
        "Order processing automation",
        "Customer relationship management",
        "Analytics and reporting",
      ],
      useCases: ["Online retail stores", "B2B marketplaces", "Subscription services", "Digital product sales"],
    },
    {
      icon: Mail,
      title: "Professional Email Services",
      description: "Business email hosting with Microsoft 365, Google Workspace, and custom email solutions.",
      features: [
        "Microsoft 365 Business Email",
        "Google Workspace",
        "cPanel email hosting",
        "Email migration services",
        "Spam and virus protection",
        "Mobile device synchronization",
      ],
      useCases: ["Corporate email", "Team collaboration", "Professional communication", "Email archiving"],
    },
    {
      icon: Cloud,
      title: "Enterprise Resource Planning (ERP)",
      description: "Integrated ERP solutions to streamline operations and improve business efficiency.",
      features: [
        "Custom ERP development",
        "Financial management",
        "Inventory and supply chain",
        "Human resources management",
        "Customer relationship management",
        "Business intelligence and reporting",
      ],
      useCases: ["Manufacturing", "Distribution", "Healthcare", "Education institutions"],
    },
    {
      icon: Database,
      title: "Database Design & Management",
      description: "Robust database solutions for efficient data storage, retrieval, and management.",
      features: [
        "Database architecture design",
        "SQL and NoSQL databases",
        "Database optimization",
        "Data migration services",
        "Backup and recovery solutions",
        "Database security",
      ],
      useCases: ["Data warehousing", "Transaction processing", "Analytics platforms", "Content management"],
    },
    {
      icon: Code,
      title: "Domain Registration",
      description: "Official .mw domain registration as an accredited SMDP distributor, plus international domains.",
      features: [
        "Accredited .mw domain registrar",
        "International domain registration",
        "Domain transfer services",
        "DNS management",
        "Domain privacy protection",
        "Bulk domain registration",
      ],
      useCases: ["Business websites", "Brand protection", "Email hosting", "Web applications"],
    },
    {
      icon: Briefcase,
      title: "Microsoft 365 Distribution",
      description: "Authorized Microsoft 365 partner providing licensing, deployment, and training services.",
      features: [
        "Microsoft 365 licensing",
        "Deployment and configuration",
        "User training and onboarding",
        "Migration from legacy systems",
        "Ongoing technical support",
        "License management",
      ],
      useCases: ["Office productivity", "Team collaboration", "Cloud storage", "Business communication"],
    },
    {
      icon: GraduationCap,
      title: "IT Training & Consultation",
      description: "Comprehensive training programs and expert consultation to maximize your technology investments.",
      features: [
        "Microsoft 365 training",
        "Software development training",
        "Cybersecurity awareness",
        "IT strategy consultation",
        "Digital transformation planning",
        "Technology assessment",
      ],
      useCases: ["Staff training", "Technology adoption", "Digital transformation", "IT strategy development"],
    },
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="pt-32 pb-16 lg:pt-40 lg:pb-24 bg-gradient-to-br from-primary/5 via-background to-accent/5">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl lg:text-6xl font-bold text-foreground mb-6 text-balance">
              Comprehensive <span className="text-accent">Technology Services</span>
            </h1>
            <p className="text-lg lg:text-xl text-muted-foreground text-pretty leading-relaxed">
              From software development to cloud hosting, we provide end-to-end technology solutions that drive business
              growth across Malawi.
            </p>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="space-y-16">
            {services.map((service, index) => {
              const Icon = service.icon
              return (
                <div
                  key={index}
                  className={`grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-start ${
                    index % 2 === 1 ? "lg:flex-row-reverse" : ""
                  }`}
                >
                  <div className={`${index % 2 === 1 ? "lg:order-2" : ""}`}>
                    <Card className="h-full border-2 hover:border-accent transition-colors">
                      <CardContent className="p-8">
                        <div className="w-16 h-16 rounded-xl bg-accent/10 flex items-center justify-center mb-6">
                          <Icon className="w-8 h-8 text-accent" />
                        </div>
                        <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-4">{service.title}</h2>
                        <p className="text-lg text-muted-foreground mb-6 leading-relaxed">{service.description}</p>
                        <Button className="bg-accent hover:bg-accent/90">
                          Learn More
                          <ArrowRight className="w-4 h-4 ml-2" />
                        </Button>
                      </CardContent>
                    </Card>
                  </div>

                  <div className={`space-y-6 ${index % 2 === 1 ? "lg:order-1" : ""}`}>
                    <div>
                      <h3 className="text-lg font-semibold text-foreground mb-4">Key Features</h3>
                      <ul className="space-y-2">
                        {service.features.map((feature, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <CheckCircle2 className="w-5 h-5 text-accent mt-0.5 flex-shrink-0" />
                            <span className="text-muted-foreground">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold text-foreground mb-4">Common Use Cases</h3>
                      <div className="flex flex-wrap gap-2">
                        {service.useCases.map((useCase, idx) => (
                          <span key={idx} className="px-3 py-1 bg-accent/10 text-accent text-sm rounded-full">
                            {useCase}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-4 lg:px-8">
          <Card className="bg-gradient-to-br from-primary to-primary/80 text-primary-foreground border-0">
            <CardContent className="p-8 lg:p-12 text-center">
              <h2 className="text-3xl lg:text-4xl font-bold mb-4 text-balance">Ready to Get Started?</h2>
              <p className="text-lg text-primary-foreground/90 mb-8 max-w-2xl mx-auto text-pretty">
                Let's discuss your project requirements and create a custom solution that fits your business needs and
                budget.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="bg-accent hover:bg-accent/90 text-accent-foreground">
                  Request a Quote
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="bg-transparent border-primary-foreground/20 hover:bg-primary-foreground/10"
                >
                  View Pricing
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      <NewsletterSection />
      <Footer />
    </div>
  )
}
