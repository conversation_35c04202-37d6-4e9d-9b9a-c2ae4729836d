"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Footer } from "@/components/footer"
import { Search, CheckCircle2, Globe, Shield, Clock } from "lucide-react"
import { useState } from "react"

export default function DomainsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<Array<{ domain: string; available: boolean; price: string }>>([])

  const handleSearch = () => {
    // Simulate domain search
    const extensions = [".mw", ".com", ".co.mw", ".org.mw"]
    const results = extensions.map((ext) => ({
      domain: searchQuery + ext,
      available: Math.random() > 0.5,
      price: ext === ".mw" ? "MWK 25,000/year" : "MWK 15,000/year",
    }))
    setSearchResults(results)
  }

  const reasons = [
    {
      icon: Globe,
      title: "Establish Your Online Presence",
      description: "A professional domain name builds credibility and makes your business easy to find online.",
    },
    {
      icon: Shield,
      title: "Protect Your Brand",
      description: "Secure your business name across multiple domain extensions to prevent competitors from using it.",
    },
    {
      icon: Clock,
      title: "Quick Registration",
      description: "Register your domain in minutes with our streamlined process and instant activation.",
    },
  ]

  const features = [
    "Free DNS management",
    "Domain privacy protection available",
    "Easy domain transfer",
    "Auto-renewal options",
    "24/7 customer support",
    "Free email forwarding",
  ]

  return (
    <div className="min-h-screen pt-16">
      <section className="py-16 lg:py-20 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h1 className="text-3xl lg:text-4xl font-bold text-foreground mb-4 text-balance">
              Find Your Perfect Domain Name
            </h1>
            <p className="text-base lg:text-lg text-muted-foreground mb-8 text-pretty">
              Accredited SMDP distributor of .mw domains. Register your Malawian domain name today and establish your
              online presence.
            </p>
          </div>

          <div className="max-w-2xl mx-auto">
            <Card className="border-2 border-primary/20">
              <CardContent className="p-6">
                <div className="flex flex-col sm:flex-row gap-3">
                  <div className="flex-1 relative">
                    <Input
                      type="text"
                      placeholder="Enter your desired domain name..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                      className="pr-10"
                    />
                    <Search className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                  </div>
                  <Button onClick={handleSearch} className="bg-primary hover:bg-primary/90">
                    Search Domain
                  </Button>
                </div>

                {searchResults.length > 0 && (
                  <div className="mt-6 space-y-3">
                    {searchResults.map((result, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-4 rounded-lg border bg-background"
                      >
                        <div className="flex items-center gap-3">
                          <div className={`w-2 h-2 rounded-full ${result.available ? "bg-green-500" : "bg-red-500"}`} />
                          <span className="font-medium text-foreground">{result.domain}</span>
                          <span
                            className={`text-xs px-2 py-1 rounded ${result.available ? "bg-green-100 text-green-700" : "bg-red-100 text-red-700"}`}
                          >
                            {result.available ? "Available" : "Taken"}
                          </span>
                        </div>
                        <div className="flex items-center gap-3">
                          <span className="text-sm font-semibold text-foreground">{result.price}</span>
                          {result.available && (
                            <Button size="sm" className="bg-primary hover:bg-primary/90">
                              Add to Cart
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3">3 Reasons to Register Today</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Why you should secure your domain name now
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
            {reasons.map((reason, index) => {
              const Icon = reason.icon
              return (
                <Card key={index} className="border-2 hover:border-primary transition-colors">
                  <CardContent className="p-6 text-center">
                    <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4 mx-auto">
                      <Icon className="w-6 h-6 text-primary" />
                    </div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">{reason.title}</h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">{reason.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20 bg-muted/30">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-6">Why Choose DreamCode for Domains</h2>
              <p className="text-base text-muted-foreground mb-6 leading-relaxed">
                As an accredited SMDP distributor, we are authorized to register and manage .mw domains. We provide
                reliable domain services with local support.
              </p>
              <ul className="space-y-3">
                {features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <CheckCircle2 className="w-5 h-5 text-primary flex-shrink-0 mt-0.5" />
                    <span className="text-sm text-foreground">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
            <Card className="bg-gradient-to-br from-primary/5 to-accent/5 border-2">
              <CardContent className="p-8">
                <h3 className="text-xl font-bold text-foreground mb-6">Domain Pricing</h3>
                <div className="space-y-4">
                  {[
                    { extension: ".mw", price: "MWK 25,000", period: "/year" },
                    { extension: ".com", price: "MWK 15,000", period: "/year" },
                    { extension: ".co.mw", price: "MWK 20,000", period: "/year" },
                    { extension: ".org.mw", price: "MWK 20,000", period: "/year" },
                    { extension: ".net", price: "MWK 15,000", period: "/year" },
                  ].map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-4 rounded-lg bg-background border">
                      <span className="font-semibold text-foreground">{item.extension}</span>
                      <div className="text-right">
                        <span className="text-lg font-bold text-primary">{item.price}</span>
                        <span className="text-sm text-muted-foreground">{item.period}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 lg:px-8 text-center">
          <h2 className="text-2xl lg:text-3xl font-bold mb-4">Ready to Register Your Domain?</h2>
          <p className="text-base text-primary-foreground/90 mb-8 max-w-2xl mx-auto">
            Search for your perfect domain name and get started today. Our team is here to help you every step of the
            way.
          </p>
          <Button size="lg" className="bg-accent hover:bg-accent/90 text-accent-foreground">
            Search Domains Now
          </Button>
        </div>
      </section>

      <Footer />
    </div>
  )
}
