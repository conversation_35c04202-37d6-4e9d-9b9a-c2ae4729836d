import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Footer } from "@/components/footer"
import {
  Server,
  Network,
  Settings,
  Download,
  Shield,
  HardDrive,
  Wifi,
  Monitor,
  Printer,
  Smartphone,
  ArrowRight,
  CheckCircle2,
} from "lucide-react"

export default function ITServicesPage() {
  const services = [
    {
      icon: Server,
      title: "Server Setup & Configuration",
      description:
        "Professional server installation and configuration for Windows Server and Linux distributions including Ubuntu, CentOS, and Debian.",
      features: [
        "Windows Server 2019/2022 setup",
        "Linux server installation (Ubuntu, CentOS, Debian)",
        "Active Directory configuration",
        "File server setup",
        "Database server configuration",
        "Web server deployment",
      ],
    },
    {
      icon: Network,
      title: "Network Setup & Installation",
      description:
        "Complete network infrastructure design, installation, and configuration for businesses of all sizes.",
      features: [
        "Network design and planning",
        "Router and switch configuration",
        "Wireless network setup",
        "VPN configuration",
        "Network security implementation",
        "Cable installation and management",
      ],
    },
    {
      icon: Settings,
      title: "Software Configuration & Administration",
      description: "Expert configuration and ongoing administration of business software and systems.",
      features: [
        "Desktop software installation",
        "Printer setup and configuration",
        "Mobile device management",
        "Email client configuration",
        "Cloud service integration",
        "User account management",
      ],
    },
    {
      icon: Download,
      title: "Software Installation & Updates",
      description: "Keep your systems secure and up-to-date with our software management services.",
      features: [
        "Operating system installation",
        "Software deployment",
        "Security patch management",
        "System updates",
        "Driver installation",
        "Software licensing management",
      ],
    },
    {
      icon: Shield,
      title: "IT Security Services",
      description: "Protect your business from cyber threats with comprehensive security solutions.",
      features: [
        "Firewall configuration",
        "Antivirus deployment",
        "Security audits",
        "Data backup solutions",
        "Disaster recovery planning",
        "Security training",
      ],
    },
    {
      icon: HardDrive,
      title: "Data Backup & Recovery",
      description: "Ensure your critical business data is protected and recoverable.",
      features: [
        "Automated backup setup",
        "Cloud backup solutions",
        "Local backup systems",
        "Data recovery services",
        "Backup monitoring",
        "Disaster recovery testing",
      ],
    },
  ]

  const supportPackages = [
    {
      name: "Basic Support",
      price: "MWK 50,000",
      period: "/month",
      description: "Essential IT support for small businesses",
      features: [
        "Email support (24-hour response)",
        "Remote assistance",
        "Monthly system check",
        "Software updates",
        "Basic troubleshooting",
      ],
    },
    {
      name: "Standard Support",
      price: "MWK 100,000",
      period: "/month",
      description: "Comprehensive support for growing businesses",
      features: [
        "Priority email & phone support",
        "Remote and on-site assistance",
        "Weekly system monitoring",
        "Proactive maintenance",
        "Security updates",
        "Quarterly IT review",
      ],
      popular: true,
    },
    {
      name: "Premium Support",
      price: "MWK 200,000",
      period: "/month",
      description: "Full-service IT management",
      features: [
        "24/7 support hotline",
        "Dedicated IT consultant",
        "Daily system monitoring",
        "Proactive issue resolution",
        "Priority on-site visits",
        "Monthly IT strategy sessions",
        "Disaster recovery planning",
      ],
    },
  ]

  return (
    <div className="min-h-screen pt-16">
      <section className="py-16 lg:py-20 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl lg:text-4xl font-bold text-foreground mb-4 text-balance">
              Professional IT Services
            </h1>
            <p className="text-base lg:text-lg text-muted-foreground mb-8 text-pretty">
              Comprehensive IT infrastructure and support services to keep your business running smoothly. From server
              setup to ongoing maintenance, we've got you covered.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-primary hover:bg-primary/90">
                Get Started
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button size="lg" variant="outline">
                Contact Us
              </Button>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3">Our IT Services</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Complete IT infrastructure and support solutions for your business
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((service, index) => {
              const Icon = service.icon
              return (
                <Card key={index} className="border-2 hover:border-primary transition-colors">
                  <CardContent className="p-6">
                    <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                      <Icon className="w-6 h-6 text-primary" />
                    </div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">{service.title}</h3>
                    <p className="text-sm text-muted-foreground mb-4 leading-relaxed">{service.description}</p>
                    <ul className="space-y-2">
                      {service.features.map((feature, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <CheckCircle2 className="w-4 h-4 text-primary flex-shrink-0 mt-0.5" />
                          <span className="text-xs text-foreground">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20 bg-muted/30">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3">IT Support Packages</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Choose the right level of support for your business needs
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
            {supportPackages.map((pkg, index) => (
              <Card
                key={index}
                className={`relative ${pkg.popular ? "border-primary border-2 shadow-lg" : "border-2"}`}
              >
                {pkg.popular && (
                  <div className="absolute -top-4 left-1/2 -translate-x-1/2">
                    <span className="px-4 py-1 rounded-full bg-primary text-primary-foreground text-xs font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-foreground mb-2">{pkg.name}</h3>
                    <p className="text-sm text-muted-foreground mb-4">{pkg.description}</p>
                    <div>
                      <span className="text-3xl font-bold text-foreground">{pkg.price}</span>
                      <span className="text-muted-foreground">{pkg.period}</span>
                    </div>
                  </div>
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start gap-2">
                        <CheckCircle2 className="w-5 h-5 text-primary flex-shrink-0 mt-0.5" />
                        <span className="text-sm text-foreground">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button
                    className={`w-full ${pkg.popular ? "bg-primary" : ""}`}
                    variant={pkg.popular ? "default" : "outline"}
                  >
                    Choose Plan
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-6">Why Choose Our IT Services</h2>
              <p className="text-base text-muted-foreground mb-6 leading-relaxed">
                We provide reliable, professional IT services that keep your business technology running smoothly. Our
                experienced team understands the unique challenges of Malawian businesses.
              </p>
              <ul className="space-y-4">
                <li className="flex items-start gap-3">
                  <CheckCircle2 className="w-5 h-5 mt-0.5 flex-shrink-0 text-primary" />
                  <div>
                    <span className="font-semibold text-foreground">Experienced Technicians</span>
                    <p className="text-sm text-muted-foreground mt-1">
                      Certified professionals with years of experience in IT infrastructure
                    </p>
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <CheckCircle2 className="w-5 h-5 mt-0.5 flex-shrink-0 text-primary" />
                  <div>
                    <span className="font-semibold text-foreground">Fast Response Times</span>
                    <p className="text-sm text-muted-foreground mt-1">
                      Quick response to your IT issues to minimize downtime
                    </p>
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <CheckCircle2 className="w-5 h-5 mt-0.5 flex-shrink-0 text-primary" />
                  <div>
                    <span className="font-semibold text-foreground">Proactive Maintenance</span>
                    <p className="text-sm text-muted-foreground mt-1">
                      Regular system checks to prevent issues before they occur
                    </p>
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <CheckCircle2 className="w-5 h-5 mt-0.5 flex-shrink-0 text-primary" />
                  <div>
                    <span className="font-semibold text-foreground">Local Support</span>
                    <p className="text-sm text-muted-foreground mt-1">
                      On-site support available across Malawi when you need it
                    </p>
                  </div>
                </li>
              </ul>
            </div>
            <Card className="bg-gradient-to-br from-primary/5 to-accent/5 border-2">
              <CardContent className="p-8">
                <h3 className="text-xl font-bold text-foreground mb-6">Technologies We Support</h3>
                <div className="grid grid-cols-2 gap-4">
                  {[
                    { icon: Monitor, label: "Windows" },
                    { icon: Server, label: "Linux" },
                    { icon: Network, label: "Cisco" },
                    { icon: Wifi, label: "Ubiquiti" },
                    { icon: Printer, label: "HP/Canon" },
                    { icon: Smartphone, label: "Mobile Devices" },
                  ].map((item, index) => {
                    const Icon = item.icon
                    return (
                      <div
                        key={index}
                        className="flex items-center gap-3 p-4 rounded-lg bg-background border hover:border-primary transition-colors"
                      >
                        <Icon className="w-6 h-6 text-primary" />
                        <span className="text-sm font-medium text-foreground">{item.label}</span>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 lg:px-8 text-center">
          <h2 className="text-2xl lg:text-3xl font-bold mb-4">Need IT Support?</h2>
          <p className="text-base text-primary-foreground/90 mb-8 max-w-2xl mx-auto">
            Get in touch with our IT team to discuss your infrastructure needs and find the right support package for
            your business.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-accent hover:bg-accent/90 text-accent-foreground">
              Request Consultation
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground/10"
            >
              Call Us Now
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
