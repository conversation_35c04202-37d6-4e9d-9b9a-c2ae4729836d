import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Footer } from "@/components/footer"
import { Smartphone, Globe, ShoppingCart, Database, Code2, Layers, ArrowRight, CheckCircle2 } from "lucide-react"

export default function SoftwareDevelopmentPage() {
  const capabilities = [
    {
      icon: Smartphone,
      title: "Mobile App Development",
      description:
        "Native iOS and Android apps, as well as cross-platform solutions using Flutter and React Native for maximum reach.",
      technologies: ["Flutter", "React Native", "Swift", "Kotlin"],
    },
    {
      icon: Globe,
      title: "Web Application Development",
      description:
        "Modern, responsive web applications built with cutting-edge frameworks and optimized for performance.",
      technologies: ["React", "Next.js", "Node.js", "Python"],
    },
    {
      icon: Database,
      title: "Database Design & Management",
      description: "Scalable database architectures with proper indexing, optimization, and backup strategies.",
      technologies: ["PostgreSQL", "MongoDB", "MySQL", "Redis"],
    },
    {
      icon: ShoppingCart,
      title: "E-Commerce Solutions",
      description: "Complete online stores with payment integration, inventory management, and customer analytics.",
      technologies: ["Shopify", "WooCommerce", "Custom Solutions"],
    },
    {
      icon: Layers,
      title: "ERP Systems",
      description: "Enterprise resource planning solutions that integrate all aspects of your business operations.",
      technologies: ["Custom ERP", "Odoo", "SAP Integration"],
    },
    {
      icon: Code2,
      title: "API Development & Integration",
      description: "RESTful and GraphQL APIs, third-party integrations, and microservices architecture.",
      technologies: ["REST", "GraphQL", "Microservices"],
    },
  ]

  const inHouseApps = [
    {
      name: "Maji Water Management",
      description: "Complete water utility management system with billing, customer service, and monitoring.",
      features: ["Customer Management", "Billing & Invoicing", "Mobile App", "Reporting Dashboard"],
      demoLink: "#",
    },
    {
      name: "SchoolPro Management System",
      description: "Comprehensive school management platform for student records, grades, and communication.",
      features: ["Student Records", "Grade Management", "Parent Portal", "Fee Management"],
      demoLink: "#",
    },
    {
      name: "HealthCare Pro",
      description: "Hospital management system with patient records, appointments, and billing integration.",
      features: ["Patient Management", "Appointment Scheduling", "Billing", "Pharmacy Management"],
      demoLink: "#",
    },
  ]

  const process = [
    {
      step: "1",
      title: "Discovery & Planning",
      description: "We understand your requirements, goals, and constraints to create a detailed project plan.",
    },
    {
      step: "2",
      title: "Design & Prototyping",
      description: "Create wireframes and prototypes to visualize the solution before development begins.",
    },
    {
      step: "3",
      title: "Development",
      description: "Build your solution using agile methodology with regular updates and feedback loops.",
    },
    {
      step: "4",
      title: "Testing & QA",
      description: "Rigorous testing to ensure quality, security, and performance meet our high standards.",
    },
    {
      step: "5",
      title: "Deployment & Support",
      description: "Launch your solution and provide ongoing maintenance and support.",
    },
  ]

  return (
    <div className="min-h-screen pt-16">
      <section className="py-16 lg:py-20 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl lg:text-4xl font-bold text-foreground mb-4 text-balance">
              Custom Software Development
            </h1>
            <p className="text-base lg:text-lg text-muted-foreground mb-8 text-pretty">
              We build scalable, secure, and user-friendly software solutions tailored to your business needs. From
              mobile apps to enterprise systems, we deliver quality software that drives results.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-primary hover:bg-primary/90">
                Start Your Project
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button size="lg" variant="outline">
                View Portfolio
              </Button>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3">Our Capabilities</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Full-stack development expertise across multiple platforms and technologies
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {capabilities.map((capability, index) => {
              const Icon = capability.icon
              return (
                <Card key={index} className="border-2 hover:border-primary transition-colors">
                  <CardContent className="p-6">
                    <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                      <Icon className="w-6 h-6 text-primary" />
                    </div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">{capability.title}</h3>
                    <p className="text-sm text-muted-foreground mb-4 leading-relaxed">{capability.description}</p>
                    <div className="flex flex-wrap gap-2">
                      {capability.technologies.map((tech, idx) => (
                        <span key={idx} className="px-2 py-1 rounded bg-muted text-xs font-medium text-foreground">
                          {tech}
                        </span>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20 bg-muted/30">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3">Our SaaS Applications</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Ready-to-use software solutions that can be customized for your organization
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {inHouseApps.map((app, index) => (
              <Card key={index} className="border-2 hover:border-primary transition-colors">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-foreground mb-3">{app.name}</h3>
                  <p className="text-sm text-muted-foreground mb-4 leading-relaxed">{app.description}</p>
                  <ul className="space-y-2 mb-6">
                    {app.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start gap-2">
                        <CheckCircle2 className="w-4 h-4 text-primary flex-shrink-0 mt-0.5" />
                        <span className="text-sm text-foreground">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <div className="flex gap-3">
                    <Button size="sm" className="flex-1 bg-primary hover:bg-primary/90">
                      Request Demo
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1 bg-transparent">
                      Learn More
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3">Our Development Process</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              A proven methodology that ensures quality and timely delivery
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="space-y-6">
              {process.map((item, index) => (
                <Card key={index} className="border-2 hover:border-primary transition-colors">
                  <CardContent className="p-6">
                    <div className="flex gap-4">
                      <div className="w-12 h-12 rounded-full bg-primary text-primary-foreground flex items-center justify-center font-bold text-lg flex-shrink-0">
                        {item.step}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-foreground mb-2">{item.title}</h3>
                        <p className="text-sm text-muted-foreground leading-relaxed">{item.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 lg:px-8 text-center">
          <h2 className="text-2xl lg:text-3xl font-bold mb-4">Ready to Build Your Solution?</h2>
          <p className="text-base text-primary-foreground/90 mb-8 max-w-2xl mx-auto">
            Let's discuss your project requirements and create a solution that meets your business goals.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-accent hover:bg-accent/90 text-accent-foreground">
              Request a Quote
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground/10"
            >
              Schedule Consultation
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
