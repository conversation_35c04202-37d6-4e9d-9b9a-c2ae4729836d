import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Server, Globe, FileText, CreditCard, AlertCircle, CheckCircle, TrendingUp, ArrowRight } from "lucide-react"
import Link from "next/link"

export default function DashboardPage() {
  const services = [
    {
      icon: Server,
      name: "Cloud Hosting - Business Plan",
      status: "Active",
      expiry: "May 15, 2024",
      statusColor: "text-green-600",
    },
    {
      icon: Globe,
      name: "dreamcodemw.com",
      status: "Active",
      expiry: "December 20, 2024",
      statusColor: "text-green-600",
    },
    {
      icon: Server,
      name: "VPS Server - Standard",
      status: "Active",
      expiry: "June 30, 2024",
      statusColor: "text-green-600",
    },
  ]

  const recentInvoices = [
    {
      id: "INV-2024-0156",
      description: "Cloud Hosting - Business Plan",
      amount: "MWK 45,000",
      date: "March 15, 2024",
      status: "Paid",
      statusColor: "bg-green-100 text-green-700",
    },
    {
      id: "INV-2024-0145",
      description: "Domain Renewal - dreamcodemw.com",
      amount: "MWK 15,000",
      date: "March 1, 2024",
      status: "Paid",
      statusColor: "bg-green-100 text-green-700",
    },
    {
      id: "INV-2024-0134",
      description: "VPS Server - Standard",
      amount: "MWK 85,000",
      date: "February 20, 2024",
      status: "Paid",
      statusColor: "bg-green-100 text-green-700",
    },
  ]

  const stats = [
    {
      icon: Server,
      label: "Active Services",
      value: "3",
      change: "+1 this month",
    },
    {
      icon: FileText,
      label: "Open Tickets",
      value: "1",
      change: "Response pending",
    },
    {
      icon: CreditCard,
      label: "Outstanding Balance",
      value: "MWK 0",
      change: "All paid",
    },
    {
      icon: TrendingUp,
      label: "Total Spent",
      value: "MWK 1.2M",
      change: "Last 12 months",
    },
  ]

  return (
    <div className="min-h-screen pt-16 bg-muted/30">
      <div className="container mx-auto px-4 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-2xl lg:text-3xl font-bold text-foreground mb-2">Welcome back, John!</h1>
          <p className="text-sm text-muted-foreground">Here's an overview of your account and services</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <Card key={index} className="border-2">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                      <Icon className="w-5 h-5 text-primary" />
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground mb-1">{stat.label}</p>
                  <p className="text-2xl font-bold text-foreground mb-1">{stat.value}</p>
                  <p className="text-xs text-muted-foreground">{stat.change}</p>
                </CardContent>
              </Card>
            )
          })}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <Card className="lg:col-span-2 border-2">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-bold text-foreground">Active Services</h2>
                <Link href="/dashboard/services">
                  <Button variant="ghost" size="sm" className="text-primary hover:bg-transparent">
                    View All
                    <ArrowRight className="w-4 h-4 ml-1" />
                  </Button>
                </Link>
              </div>

              <div className="space-y-4">
                {services.map((service, index) => {
                  const Icon = service.icon
                  return (
                    <div
                      key={index}
                      className="flex items-center justify-between p-4 rounded-lg border-2 hover:border-primary transition-colors"
                    >
                      <div className="flex items-center gap-4">
                        <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                          <Icon className="w-5 h-5 text-primary" />
                        </div>
                        <div>
                          <p className="font-semibold text-foreground text-sm">{service.name}</p>
                          <p className="text-xs text-muted-foreground">Expires: {service.expiry}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className={`w-4 h-4 ${service.statusColor}`} />
                        <span className={`text-sm font-medium ${service.statusColor}`}>{service.status}</span>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          <Card className="border-2 bg-primary text-primary-foreground">
            <CardContent className="p-6">
              <AlertCircle className="w-10 h-10 mb-4" />
              <h3 className="text-lg font-bold mb-2">Service Renewal</h3>
              <p className="text-sm text-primary-foreground/90 mb-4">
                Your Cloud Hosting plan expires in 45 days. Renew now to avoid service interruption.
              </p>
              <Button className="w-full bg-accent hover:bg-accent/90 text-accent-foreground">Renew Now</Button>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="border-2">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-bold text-foreground">Recent Invoices</h2>
                <Link href="/dashboard/invoices">
                  <Button variant="ghost" size="sm" className="text-primary hover:bg-transparent">
                    View All
                    <ArrowRight className="w-4 h-4 ml-1" />
                  </Button>
                </Link>
              </div>

              <div className="space-y-4">
                {recentInvoices.map((invoice, index) => (
                  <div key={index} className="flex items-center justify-between p-4 rounded-lg border">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="font-semibold text-foreground text-sm">{invoice.id}</p>
                        <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${invoice.statusColor}`}>
                          {invoice.status}
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground mb-1">{invoice.description}</p>
                      <p className="text-xs text-muted-foreground">{invoice.date}</p>
                    </div>
                    <p className="font-bold text-foreground">{invoice.amount}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="border-2">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-bold text-foreground">Quick Actions</h2>
              </div>

              <div className="space-y-3">
                <Link href="/dashboard/services/new">
                  <Button variant="outline" className="w-full justify-start bg-transparent">
                    <Server className="w-4 h-4 mr-2" />
                    Order New Service
                  </Button>
                </Link>
                <Link href="/dashboard/domains">
                  <Button variant="outline" className="w-full justify-start bg-transparent">
                    <Globe className="w-4 h-4 mr-2" />
                    Register Domain
                  </Button>
                </Link>
                <Link href="/dashboard/support">
                  <Button variant="outline" className="w-full justify-start bg-transparent">
                    <FileText className="w-4 h-4 mr-2" />
                    Open Support Ticket
                  </Button>
                </Link>
                <Link href="/dashboard/invoices">
                  <Button variant="outline" className="w-full justify-start bg-transparent">
                    <CreditCard className="w-4 h-4 mr-2" />
                    View Invoices
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
