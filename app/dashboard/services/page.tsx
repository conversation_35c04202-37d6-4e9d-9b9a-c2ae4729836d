import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Server, Globe, Shield, Settings, CheckCircle, Plus } from "lucide-react"
import Link from "next/link"

export default function ServicesPage() {
  const services = [
    {
      icon: Server,
      name: "Cloud Hosting - Business Plan",
      type: "Web Hosting",
      status: "Active",
      statusColor: "text-green-600",
      statusIcon: CheckCircle,
      details: {
        plan: "Business Plan",
        storage: "50 GB SSD",
        bandwidth: "Unlimited",
        email: "25 Email Accounts",
      },
      expiry: "May 15, 2024",
      autoRenew: true,
      price: "MWK 45,000/month",
    },
    {
      icon: Globe,
      name: "dreamcodemw.com",
      type: "Domain Name",
      status: "Active",
      statusColor: "text-green-600",
      statusIcon: CheckCircle,
      details: {
        registrar: "DreamCode Malawi",
        nameservers: "ns1.dreamcodemw.com",
        privacy: "Enabled",
        autoRenew: "Enabled",
      },
      expiry: "December 20, 2024",
      autoRenew: true,
      price: "MWK 15,000/year",
    },
    {
      icon: Server,
      name: "VPS Server - Standard",
      type: "VPS Hosting",
      status: "Active",
      statusColor: "text-green-600",
      statusIcon: CheckCircle,
      details: {
        cpu: "4 vCPU Cores",
        ram: "8 GB RAM",
        storage: "160 GB SSD",
        bandwidth: "4 TB Transfer",
      },
      expiry: "June 30, 2024",
      autoRenew: true,
      price: "MWK 85,000/month",
    },
    {
      icon: Shield,
      name: "SSL Certificate - Wildcard",
      type: "Security",
      status: "Active",
      statusColor: "text-green-600",
      statusIcon: CheckCircle,
      details: {
        type: "Wildcard SSL",
        coverage: "*.dreamcodemw.com",
        validation: "Domain Validated",
        encryption: "256-bit",
      },
      expiry: "August 10, 2024",
      autoRenew: true,
      price: "MWK 35,000/year",
    },
  ]

  return (
    <div className="min-h-screen pt-16 bg-muted/30">
      <div className="container mx-auto px-4 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-2xl lg:text-3xl font-bold text-foreground mb-2">My Services</h1>
            <p className="text-sm text-muted-foreground">Manage your active services and subscriptions</p>
          </div>
          <Link href="/dashboard/services/new">
            <Button className="bg-primary hover:bg-primary/90">
              <Plus className="w-4 h-4 mr-2" />
              Order New Service
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {services.map((service, index) => {
            const Icon = service.icon
            const StatusIcon = service.statusIcon
            return (
              <Card key={index} className="border-2 hover:border-primary transition-colors">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0">
                        <Icon className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-bold text-foreground mb-1">{service.name}</h3>
                        <p className="text-xs text-muted-foreground mb-2">{service.type}</p>
                        <div className="flex items-center gap-2">
                          <StatusIcon className={`w-4 h-4 ${service.statusColor}`} />
                          <span className={`text-sm font-medium ${service.statusColor}`}>{service.status}</span>
                        </div>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>

                  <div className="space-y-2 mb-4 p-4 rounded-lg bg-muted/50">
                    {Object.entries(service.details).map(([key, value], idx) => (
                      <div key={idx} className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground capitalize">{key.replace(/([A-Z])/g, " $1")}</span>
                        <span className="font-medium text-foreground">{value}</span>
                      </div>
                    ))}
                  </div>

                  <div className="flex items-center justify-between mb-4 pb-4 border-b">
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Renewal Date</p>
                      <p className="text-sm font-semibold text-foreground">{service.expiry}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-muted-foreground mb-1">Price</p>
                      <p className="text-sm font-semibold text-foreground">{service.price}</p>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1 bg-transparent">
                      Manage
                    </Button>
                    <Button size="sm" className="flex-1 bg-primary hover:bg-primary/90">
                      Renew Now
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        <div className="mt-8">
          <Card className="border-2 bg-gradient-to-br from-primary/10 to-accent/10">
            <CardContent className="p-8 text-center">
              <h2 className="text-xl font-bold text-foreground mb-3">Need Additional Services?</h2>
              <p className="text-sm text-muted-foreground mb-6 max-w-2xl mx-auto">
                Explore our full range of hosting, domain, and IT services to grow your online presence
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/services">
                  <Button variant="outline" className="bg-transparent">
                    Browse Services
                  </Button>
                </Link>
                <Link href="/contact">
                  <Button className="bg-primary hover:bg-primary/90">Contact Sales</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
