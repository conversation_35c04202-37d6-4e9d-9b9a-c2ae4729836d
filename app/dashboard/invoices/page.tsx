import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Download, Eye, CreditCard, Calendar, DollarSign } from "lucide-react"
import Link from "next/link"

export default function InvoicesPage() {
  const invoices = [
    {
      id: "INV-2024-0156",
      description: "Cloud Hosting - Business Plan (Monthly)",
      amount: "MWK 45,000",
      date: "March 15, 2024",
      dueDate: "March 30, 2024",
      status: "Paid",
      statusColor: "bg-green-100 text-green-700",
      paidDate: "March 16, 2024",
    },
    {
      id: "INV-2024-0145",
      description: "Domain Renewal - dreamcodemw.com (Annual)",
      amount: "MWK 15,000",
      date: "March 1, 2024",
      dueDate: "March 15, 2024",
      status: "Paid",
      statusColor: "bg-green-100 text-green-700",
      paidDate: "March 2, 2024",
    },
    {
      id: "INV-2024-0134",
      description: "VPS Server - Standard (Monthly)",
      amount: "MWK 85,000",
      date: "February 20, 2024",
      dueDate: "March 5, 2024",
      status: "Paid",
      statusColor: "bg-green-100 text-green-700",
      paidDate: "February 21, 2024",
    },
    {
      id: "INV-2024-0123",
      description: "SSL Certificate - Wildcard (Annual)",
      amount: "MWK 35,000",
      date: "February 10, 2024",
      dueDate: "February 25, 2024",
      status: "Paid",
      statusColor: "bg-green-100 text-green-700",
      paidDate: "February 11, 2024",
    },
    {
      id: "INV-2024-0112",
      description: "Cloud Hosting - Business Plan (Monthly)",
      amount: "MWK 45,000",
      date: "February 15, 2024",
      dueDate: "March 1, 2024",
      status: "Paid",
      statusColor: "bg-green-100 text-green-700",
      paidDate: "February 16, 2024",
    },
  ]

  const summary = {
    totalPaid: "MWK 1,245,000",
    outstanding: "MWK 0",
    nextDue: "April 15, 2024",
    nextAmount: "MWK 45,000",
  }

  return (
    <div className="min-h-screen pt-16 bg-muted/30">
      <div className="container mx-auto px-4 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-2xl lg:text-3xl font-bold text-foreground mb-2">Invoices & Billing</h1>
          <p className="text-sm text-muted-foreground">View and manage your invoices and payment history</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="border-2">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-3">
                <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                  <DollarSign className="w-5 h-5 text-primary" />
                </div>
              </div>
              <p className="text-xs text-muted-foreground mb-1">Total Paid (12 months)</p>
              <p className="text-xl font-bold text-foreground">{summary.totalPaid}</p>
            </CardContent>
          </Card>

          <Card className="border-2">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-3">
                <div className="w-10 h-10 rounded-lg bg-green-100 flex items-center justify-center">
                  <CreditCard className="w-5 h-5 text-green-600" />
                </div>
              </div>
              <p className="text-xs text-muted-foreground mb-1">Outstanding Balance</p>
              <p className="text-xl font-bold text-green-600">{summary.outstanding}</p>
            </CardContent>
          </Card>

          <Card className="border-2">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-3">
                <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                  <Calendar className="w-5 h-5 text-primary" />
                </div>
              </div>
              <p className="text-xs text-muted-foreground mb-1">Next Payment Due</p>
              <p className="text-xl font-bold text-foreground">{summary.nextDue}</p>
            </CardContent>
          </Card>

          <Card className="border-2">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-3">
                <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                  <DollarSign className="w-5 h-5 text-primary" />
                </div>
              </div>
              <p className="text-xs text-muted-foreground mb-1">Next Payment Amount</p>
              <p className="text-xl font-bold text-foreground">{summary.nextAmount}</p>
            </CardContent>
          </Card>
        </div>

        <Card className="border-2">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-bold text-foreground">Invoice History</h2>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" className="bg-transparent">
                  Filter
                </Button>
                <Button variant="outline" size="sm" className="bg-transparent">
                  Export
                </Button>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 text-sm font-semibold text-foreground">Invoice ID</th>
                    <th className="text-left py-3 px-4 text-sm font-semibold text-foreground">Description</th>
                    <th className="text-left py-3 px-4 text-sm font-semibold text-foreground">Date</th>
                    <th className="text-left py-3 px-4 text-sm font-semibold text-foreground">Due Date</th>
                    <th className="text-left py-3 px-4 text-sm font-semibold text-foreground">Amount</th>
                    <th className="text-left py-3 px-4 text-sm font-semibold text-foreground">Status</th>
                    <th className="text-left py-3 px-4 text-sm font-semibold text-foreground">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {invoices.map((invoice, index) => (
                    <tr key={index} className="border-b hover:bg-muted/50">
                      <td className="py-4 px-4">
                        <p className="text-sm font-medium text-foreground">{invoice.id}</p>
                      </td>
                      <td className="py-4 px-4">
                        <p className="text-sm text-foreground">{invoice.description}</p>
                      </td>
                      <td className="py-4 px-4">
                        <p className="text-sm text-muted-foreground">{invoice.date}</p>
                      </td>
                      <td className="py-4 px-4">
                        <p className="text-sm text-muted-foreground">{invoice.dueDate}</p>
                      </td>
                      <td className="py-4 px-4">
                        <p className="text-sm font-semibold text-foreground">{invoice.amount}</p>
                      </td>
                      <td className="py-4 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${invoice.statusColor}`}>
                          {invoice.status}
                        </span>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex gap-2">
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <Download className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        <div className="mt-6">
          <Card className="border-2 bg-primary/5">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <CreditCard className="w-6 h-6 text-primary" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-foreground mb-2">Payment Methods</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    We accept bank transfers, mobile money (Airtel Money, TNM Mpamba), and cash payments at our office.
                  </p>
                  <Link href="/dashboard/payment-methods">
                    <Button variant="outline" size="sm" className="bg-transparent">
                      Manage Payment Methods
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
