import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Footer } from "@/components/footer"
import { FileText, Video, BookOpen, Download, ExternalLink } from "lucide-react"
import Link from "next/link"

export default function ResourcesPage() {
  const resources = [
    {
      category: "Guides & Tutorials",
      icon: BookOpen,
      items: [
        {
          title: "Getting Started with Web Hosting",
          description: "A comprehensive guide to choosing and setting up your first web hosting account.",
          type: "PDF Guide",
          link: "#",
        },
        {
          title: "Domain Registration Best Practices",
          description: "Learn how to choose the perfect domain name and protect your brand online.",
          type: "Article",
          link: "#",
        },
        {
          title: "Website Security Checklist",
          description: "Essential security measures every website owner should implement.",
          type: "Checklist",
          link: "#",
        },
        {
          title: "Email Setup Guide for Microsoft 365",
          description: "Step-by-step instructions for configuring your business email.",
          type: "PDF Guide",
          link: "#",
        },
      ],
    },
    {
      category: "Video Tutorials",
      icon: Video,
      items: [
        {
          title: "How to Use Your Hosting Control Panel",
          description: "A video walkthrough of the hosting control panel features and settings.",
          type: "Video (15 min)",
          link: "#",
        },
        {
          title: "Setting Up Email on Mobile Devices",
          description: "Configure your business email on iOS and Android devices.",
          type: "Video (10 min)",
          link: "#",
        },
        {
          title: "Website Backup and Restoration",
          description: "Learn how to backup and restore your website using our tools.",
          type: "Video (12 min)",
          link: "#",
        },
      ],
    },
    {
      category: "Technical Documentation",
      icon: FileText,
      items: [
        {
          title: "API Documentation",
          description: "Complete API reference for integrating with our services.",
          type: "Documentation",
          link: "#",
        },
        {
          title: "Server Configuration Guide",
          description: "Advanced server setup and optimization instructions.",
          type: "Technical Guide",
          link: "#",
        },
        {
          title: "Database Management Best Practices",
          description: "Guidelines for managing and optimizing your databases.",
          type: "Documentation",
          link: "#",
        },
      ],
    },
    {
      category: "Downloadable Resources",
      icon: Download,
      items: [
        {
          title: "Software Development Proposal Template",
          description: "Template for preparing software development project proposals.",
          type: "DOCX Template",
          link: "#",
        },
        {
          title: "IT Infrastructure Assessment Checklist",
          description: "Evaluate your current IT infrastructure and identify improvement areas.",
          type: "PDF Checklist",
          link: "#",
        },
        {
          title: "Website Launch Checklist",
          description: "Everything you need to check before launching your website.",
          type: "PDF Checklist",
          link: "#",
        },
      ],
    },
  ]

  const caseStudies = [
    {
      title: "Northern Region Water Board - Maji App",
      description: "How we digitized water billing and customer service for 50,000+ customers.",
      industry: "Water Utilities",
      link: "#",
    },
    {
      title: "MediCare Hospital Management System",
      description: "Streamlining healthcare operations with integrated patient management.",
      industry: "Healthcare",
      link: "#",
    },
    {
      title: "AgriConnect Marketplace Platform",
      description: "Connecting farmers with buyers through a digital marketplace.",
      industry: "Agriculture",
      link: "#",
    },
  ]

  return (
    <div className="min-h-screen pt-16">
      <section className="py-16 lg:py-20 bg-gradient-to-br from-primary/5 via-background to-accent/5">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl lg:text-4xl font-bold text-foreground mb-6 text-balance">
              Resources & <span className="text-primary">Learning Center</span>
            </h1>
            <p className="text-base lg:text-lg text-muted-foreground text-pretty leading-relaxed">
              Access guides, tutorials, documentation, and case studies to help you make the most of our services and
              technology solutions.
            </p>
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="space-y-16">
            {resources.map((section, sectionIndex) => {
              const Icon = section.icon
              return (
                <div key={sectionIndex}>
                  <div className="flex items-center gap-3 mb-8">
                    <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center">
                      <Icon className="w-6 h-6 text-primary" />
                    </div>
                    <h2 className="text-2xl lg:text-3xl font-bold text-foreground">{section.category}</h2>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {section.items.map((item, index) => (
                      <Card key={index} className="border-2 hover:border-primary transition-colors group">
                        <CardContent className="p-6">
                          <div className="flex items-start justify-between mb-3">
                            <span className="px-3 py-1 rounded-full bg-primary/10 text-primary text-xs font-medium">
                              {item.type}
                            </span>
                            <ExternalLink className="w-4 h-4 text-muted-foreground group-hover:text-primary transition-colors" />
                          </div>
                          <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-primary transition-colors">
                            {item.title}
                          </h3>
                          <p className="text-sm text-muted-foreground mb-4 leading-relaxed">{item.description}</p>
                          <Link href={item.link}>
                            <Button variant="ghost" size="sm" className="p-0 h-auto text-primary hover:bg-transparent">
                              Access Resource
                            </Button>
                          </Link>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20 bg-muted/30">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-3">Case Studies</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Real-world examples of how we've helped businesses transform through technology
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {caseStudies.map((study, index) => (
              <Card key={index} className="border-2 hover:border-primary transition-colors group">
                <CardContent className="p-6">
                  <div className="mb-3">
                    <span className="px-3 py-1 rounded-full bg-primary/10 text-primary text-xs font-medium">
                      {study.industry}
                    </span>
                  </div>
                  <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-primary transition-colors">
                    {study.title}
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4 leading-relaxed">{study.description}</p>
                  <Link href={study.link}>
                    <Button variant="ghost" size="sm" className="p-0 h-auto text-primary hover:bg-transparent">
                      Read Case Study
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <Card className="bg-gradient-to-br from-primary/10 to-accent/10 border-2 border-primary/20">
            <CardContent className="p-8 lg:p-12 text-center">
              <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-4 text-balance">
                Need Personalized Guidance?
              </h2>
              <p className="text-base text-muted-foreground mb-8 max-w-2xl mx-auto text-pretty">
                Our team is available to provide one-on-one consultation and training for your specific needs.
              </p>
              <Button size="lg" className="bg-primary hover:bg-primary/90">
                Schedule a Consultation
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>

      <Footer />
    </div>
  )
}
