import Link from "next/link";
import { Facebook, Linkedin, Mail, MapPin, Phone, Twitter } from "lucide-react";
import Image from "next/image";

export function Footer() {
  return (
    <footer className="bg-primary text-primary-foreground">
      <div className="container mx-auto px-4 lg:px-8 py-12 lg:py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
          {/* Company Info */}
          <div>
            {/* <div className="flex items-center gap-2 mb-4">
              <div className="w-10 h-10 bg-accent rounded-lg flex items-center justify-center">
                <span className="text-accent-foreground font-bold text-xl">DC</span>
              </div>
              <span className="font-bold text-xl">
                DreamCode <span className="text-accent">Malawi</span>
              </span>
            </div> */}
            <Link href="/" className="flex items-center gap-2">
              <Image src="/logo.png" alt="Logo" width={150} height={150} />
            </Link>
            <p className="text-primary-foreground/80 text-sm leading-relaxed mb-4">
              Transforming businesses across Malawi with innovative digital
              solutions and comprehensive IT services.
            </p>
            <div className="flex gap-4">
              <a
                href="https://web.facebook.com/DreamCodeMw/"
                className="text-primary-foreground/80 hover:text-accent transition-colors"
              >
                <Facebook className="w-5 h-5" />
              </a>
              <a
                href="https://mw.linkedin.com/company/dream-code-ltd"
                className="text-primary-foreground/80 hover:text-accent transition-colors"
              >
                <Linkedin className="w-5 h-5" />
              </a>
              <a
                href="https://x.com/DreamCodeMalawi"
                className="text-primary-foreground/80 hover:text-accent transition-colors"
              >
                <Twitter className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/services"
                  className="text-primary-foreground/80 hover:text-accent transition-colors text-sm"
                >
                  Services
                </Link>
              </li>
              <li>
                <Link
                  href="/projects"
                  className="text-primary-foreground/80 hover:text-accent transition-colors text-sm"
                >
                  Projects
                </Link>
              </li>
              <li>
                <Link
                  href="/pricing"
                  className="text-primary-foreground/80 hover:text-accent transition-colors text-sm"
                >
                  Pricing
                </Link>
              </li>
              <li>
                <Link
                  href="/about"
                  className="text-primary-foreground/80 hover:text-accent transition-colors text-sm"
                >
                  About Us
                </Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Services</h3>
            <ul className="space-y-2">
              <li className="text-primary-foreground/80 text-sm">
                Mobile App Development
              </li>
              <li className="text-primary-foreground/80 text-sm">
                Web Development
              </li>
              <li className="text-primary-foreground/80 text-sm">
                Cloud Hosting
              </li>
              <li className="text-primary-foreground/80 text-sm">
                Microsoft 365
              </li>
              <li className="text-primary-foreground/80 text-sm">
                Domain Registration
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Contact Us</h3>
            <ul className="space-y-3">
              <li className="flex items-start gap-2 text-primary-foreground/80 text-sm">
                <MapPin className="w-4 h-4 mt-0.5 flex-shrink-0" />
                <span>Blantyre, Malawi</span>
              </li>
              <li className="flex items-center gap-2 text-primary-foreground/80 text-sm">
                <Phone className="w-4 h-4 flex-shrink-0" />
                <span>+265 999 123 456</span>
              </li>
              <li className="flex items-center gap-2 text-primary-foreground/80 text-sm">
                <Mail className="w-4 h-4 flex-shrink-0" />
                <span><EMAIL></span>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-primary-foreground/20 mt-12 pt-8 text-center">
          <p className="text-primary-foreground/60 text-sm">
            © {new Date().getFullYear()} DreamCode Malawi. All rights reserved.
            | Accredited SMDP Distributor of .mw domains
          </p>
        </div>
      </div>
    </footer>
  );
}
