"use client";

import { useEffect, useRef } from "react";

const PARTNERS = [
  { name: "Microsoft", logo: "partners/microsoft.svg" },
  { name: "Northern Region Water Board", logo: "partners/nrwb.png" },
  { name: "Malawi Government", logo: "partners/gov.png" },
  { name: "Reserve Bank of Malawi", logo: "partners/rbm.png" },
  { name: "Airtel Malawi", logo: "partners/airtel.svg" },
  { name: "TNM", logo: "partners/tnm.svg" },
  {
    name: "Standard Bank",
    logo: "https://cdn.brandfetch.io/idnenZNina/w/350/h/84/theme/light/logo.png?c=1bxid64Mup7aczewSAYMX&t=*************",
  },
  { name: "FDH Bank", logo: "partners/fdh.webp" },
  { name: "National Bank of Malawi", logo: "partners/nbm.webp" },
  { name: "MUST", logo: "partners/must.png" },
  { name: "Blantyre Water Board", logo: "partners/bwb_logo.png" },
  { name: "ESCOM", logo: "partners/escom.png" },
];

export function PartnersCarousel() {
  const scrollRef = useRef<HTMLDivElement>(null);
  const scrollRef2 = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const scrollContainer = scrollRef.current;
    const scrollContainer2 = scrollRef2.current;
    if (!scrollContainer) return;
    if (!scrollContainer2) return;

    let scrollPosition = 0;
    const scroll = () => {
      scrollPosition += 0.5;
      if (scrollPosition >= scrollContainer.scrollWidth / 2) {
        scrollPosition = 0;
      }
      scrollContainer.scrollLeft = scrollPosition;
    };

    let scrollPosition2 = scrollContainer2.scrollWidth / 2;
    const scroll2 = () => {
      scrollPosition2 -= 0.5;
      if (scrollPosition2 <= 0) {
        scrollPosition2 = scrollContainer2.scrollWidth / 2;
      }
      scrollContainer2.scrollLeft = scrollPosition2;
    };

    const intervalId = setInterval(scroll, 20);
    const intervalId2 = setInterval(scroll2, 20);

    return () => {
      clearInterval(intervalId);
      clearInterval(intervalId2);
    };
  }, []);

  // Duplicate partners for seamless loop
  const duplicatedPartners = [...PARTNERS, ...PARTNERS];

  return (
    <div className="relative overflow-hidden py-12 bg-muted/50">
      <div className="container mx-auto px-4 lg:px-8 mb-8">
        <h2 className="text-2xl lg:text-3xl font-bold text-center text-foreground mb-2">
          Trusted By Leading Organizations
        </h2>
        <p className="text-center text-muted-foreground">
          Partnering with businesses and institutions across Malawi
        </p>
      </div>

      <div className="relative">
        {/* Gradient overlays */}
        <div className="absolute left-0 top-0 bottom-0 w-32 bg-gradient-to-r from-background to-transparent z-10" />
        <div className="absolute right-0 top-0 bottom-0 w-32 bg-gradient-to-l from-background to-transparent z-10" />

        {/* Scrolling container */}
        <div
          ref={scrollRef}
          className="flex gap-12 overflow-hidden"
          style={{ scrollBehavior: "auto" }}
        >
          {duplicatedPartners.map((partner, index) => (
            <div
              key={`${partner.name}-${index}`}
              className="flex-shrink-0 w-40 h-20 flex items-center justify-center grayscale hover:grayscale-0 transition-all opacity-60 hover:opacity-100"
            >
              <img
                src={partner.logo || "/placeholder.svg"}
                alt={partner.name}
                className="max-w-full max-h-full object-contain"
              />
            </div>
          ))}
        </div>
        <div
          ref={scrollRef2}
          className="flex gap-12 overflow-hidden"
          style={{ scrollBehavior: "auto" }}
        >
          {duplicatedPartners.map((partner, index) => (
            <div
              key={`${partner.name}-${index}`}
              className="flex-shrink-0 w-40 h-20 flex items-center justify-center grayscale hover:grayscale-0 transition-all opacity-60 hover:opacity-100"
            >
              <img
                src={partner.logo || "/placeholder.svg"}
                alt={partner.name}
                className="max-w-full max-h-full object-contain"
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
