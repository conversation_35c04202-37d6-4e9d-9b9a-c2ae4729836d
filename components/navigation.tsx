"use client";

import Link from "next/link";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Menu, X, Search, ChevronDown } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import Image from "next/image";

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const [showSearch, setShowSearch] = useState(false);

  return (
    <nav className="sticky top-0 left-0 right-0 z-50 bg-primary backdrop-blur-md border-b border-blue-500 shadow-lg">
      <div className="container mx-auto px-4 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center gap-2">
            <Image src="/logo.png" alt="Logo" width={110} height={110} />
          </Link>

          {/* Desktop Navigation */}
          <ul className="hidden lg:flex items-center gap-6">
            <li>
              <Link
                href="/"
                className="text-sm font-medium text-white hover:text-blue-100 transition-colors"
              >
                Home
              </Link>
            </li>
            <li>
              {/* Services Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger className="flex items-center gap-1 text-sm font-medium text-white hover:text-blue-100 transition-colors">
                  Services <ChevronDown className="w-4 h-4" />
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-56">
                  <DropdownMenuItem asChild>
                    <Link href="/services/cloud" className="cursor-pointer">
                      Cloud Services
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/services/domains" className="cursor-pointer">
                      Domains
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/services/software" className="cursor-pointer">
                      Software Development
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/services/it" className="cursor-pointer">
                      IT Services
                    </Link>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </li>
            <li>
              <Link
                href="/projects"
                className="text-sm font-medium text-white hover:text-blue-100 transition-colors"
              >
                Projects
              </Link>
            </li>
            <li>
              <Link
                href="/careers"
                className="text-sm font-medium text-white hover:text-blue-100 transition-colors"
              >
                Careers
              </Link>
            </li>
            <li>
              <Link
                href="/blog"
                className="text-sm font-medium text-white hover:text-blue-100 transition-colors"
              >
                Blog
              </Link>
            </li>
            <li>
              <Link
                href="/about"
                className="text-sm font-medium text-white hover:text-blue-100 transition-colors"
              >
                About Us
              </Link>
            </li>
            <li>
              <Link
                href="/contact"
                className="text-sm font-medium text-white hover:text-blue-100 transition-colors"
              >
                Contact
              </Link>
            </li>
            <li>
              <button
                onClick={() => setShowSearch(!showSearch)}
                className="p-2 text-white hover:text-blue-100 transition-colors"
                aria-label="Search"
              >
                <Search className="w-5 h-5" />
              </button>
            </li>
            <li>
              <Button
                size="sm"
                className="bg-blue-400 hover:bg-primary/90 shadow-md"
              >
                Get Started
              </Button>
            </li>
          </ul>

          {/* Mobile Menu Button */}
          <div className="flex items-center gap-2 lg:hidden">
            <button
              onClick={() => setShowSearch(!showSearch)}
              className="p-2 text-white hover:text-blue-100 transition-colors"
              aria-label="Search"
            >
              <Search className="w-5 h-5" />
            </button>
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="p-2 text-white hover:text-blue-100 transition-colors"
              aria-label="Toggle menu"
            >
              {isOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Search Bar */}
        {showSearch && (
          <div className="py-4 border-t border-border">
            <Input
              type="search"
              placeholder="Search services, projects, or resources..."
              className="w-full"
              autoFocus
            />
          </div>
        )}

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="lg:hidden py-4 border-t border-border">
            <ul className="flex flex-col gap-4">
              <li>
                <Link
                  href="/"
                  className="text-sm font-medium text-white hover:text-blue-100 transition-colors py-2"
                  onClick={() => setIsOpen(false)}
                >
                  Home
                </Link>
              </li>
              <li>
                <span className="text-sm font-semibold text-white">
                  Services
                </span>
                <ul className="flex flex-col gap-2 pl-4">
                  <li>
                    <Link
                      href="/services/cloud"
                      className="text-sm text-white hover:text-blue-100 transition-colors py-1"
                      onClick={() => setIsOpen(false)}
                    >
                      Cloud Services
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/services/domains"
                      className="text-sm text-white hover:text-blue-100 transition-colors py-1"
                      onClick={() => setIsOpen(false)}
                    >
                      Domains
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/services/software"
                      className="text-sm text-white hover:text-blue-100 transition-colors py-1"
                      onClick={() => setIsOpen(false)}
                    >
                      Software Development
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/services/it"
                      className="text-sm text-white hover:text-blue-100 transition-colors py-1"
                      onClick={() => setIsOpen(false)}
                    >
                      IT Services
                    </Link>
                  </li>
                </ul>
              </li>
              <li>
                <Link
                  href="/projects"
                  className="text-sm font-medium text-white hover:text-blue-100 transition-colors py-2"
                  onClick={() => setIsOpen(false)}
                >
                  Projects
                </Link>
              </li>
              <li>
                <Link
                  href="/careers"
                  className="text-sm font-medium text-white hover:text-blue-100 transition-colors py-2"
                  onClick={() => setIsOpen(false)}
                >
                  Careers
                </Link>
              </li>
              <li>
                <Link
                  href="/blog"
                  className="text-sm font-medium text-white hover:text-blue-100 transition-colors py-2"
                  onClick={() => setIsOpen(false)}
                >
                  Blog
                </Link>
              </li>
              <li>
                <Link
                  href="/about"
                  className="text-sm font-medium text-white hover:text-blue-100 transition-colors py-2"
                  onClick={() => setIsOpen(false)}
                >
                  About Us
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-sm font-medium text-white hover:text-blue-100 transition-colors py-2"
                  onClick={() => setIsOpen(false)}
                >
                  Contact
                </Link>
              </li>
              <li>
                <Button
                  size="sm"
                  className="bg-primary hover:bg-primary/90 w-full mt-2"
                >
                  Get Started
                </Button>
              </li>
            </ul>
          </div>
        )}
      </div>
    </nav>
  );
}
